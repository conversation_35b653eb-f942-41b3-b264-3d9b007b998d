"""
后仿时序违例确认插件

提供时序违例日志解析、自动确认、手动确认、历史记录等功能的插件。
支持GUI界面操作，包含文件选择、违例确认、导出功能等。
"""

import os
import sys
from PyQt5.QtWidgets import QAction, QMessageBox
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtGui import QIcon

# 添加插件路径到系统路径
plugin_dir = os.path.dirname(os.path.abspath(__file__))
if plugin_dir not in sys.path:
    sys.path.insert(0, plugin_dir)

from plugins.base import PluginBase


class TimingViolationPlugin(PluginBase):
    """后仿时序违例确认插件主类"""
    
    def __init__(self):
        """初始化插件"""
        super().__init__()
        self.violation_window = None
        self.main_window = None
        self.menu_action = None
        
    @property
    def name(self) -> str:
        """插件名称"""
        return "后仿时序违例确认工具"
    
    @property
    def version(self) -> str:
        """插件版本"""
        return "1.0.0"
    
    @property
    def description(self) -> str:
        """插件描述"""
        return "提供时序违例日志解析、自动确认、手动确认、历史记录等功能"
    
    def initialize(self, main_window):
        """初始化插件
        
        Args:
            main_window: 主窗口实例
        """
        try:
            self.main_window = main_window
            
            # 延迟导入主窗口类，避免循环导入
            from .timing_violation.main_window import TimingViolationWindow
            
            # 创建时序违例确认窗口
            self.violation_window = TimingViolationWindow()
            
            # 添加菜单项
            self._add_menu_item()
            
            print(f"插件 {self.name} 初始化成功")
            
        except Exception as e:
            print(f"插件 {self.name} 初始化失败: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def _add_menu_item(self):
        """添加菜单项到主窗口"""
        try:
            # 创建菜单动作
            self.menu_action = QAction(self.name, self.main_window)
            self.menu_action.setStatusTip(self.description)
            self.menu_action.triggered.connect(self._on_menu_triggered)
            
            # 添加到工具菜单
            if hasattr(self.main_window, 'tools_menu'):
                self.main_window.tools_menu.addAction(self.menu_action)
                print(f"菜单项 '{self.name}' 已添加到工具菜单")
            else:
                print("警告: 主窗口没有tools_menu属性")
                
        except Exception as e:
            print(f"添加菜单项失败: {str(e)}")
    
    def _on_menu_triggered(self):
        """菜单项被点击时的处理函数"""
        try:
            # 如果窗口尚未创建，创建新窗口
            if self.violation_window is None:
                from timing_violation.main_window import TimingViolationWindow
                self.violation_window = TimingViolationWindow()
                # 连接窗口关闭信号
                self.violation_window.finished.connect(self._on_window_closed)
            
            # 显示窗口
            self.violation_window.show()
            self.violation_window.raise_()
            self.violation_window.activateWindow()
            
        except Exception as e:
            print(f"打开后仿时序违例确认工具失败: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(
                self.main_window,
                "错误",
                f"无法打开后仿时序违例确认工具: {str(e)}\n\n请确保插件文件完整。"
            )
    
    def _on_window_closed(self):
        """窗口关闭时的处理函数"""
        self.violation_window = None
    
    def cleanup(self):
        """清理插件资源"""
        try:
            # 关闭窗口
            if self.violation_window:
                self.violation_window.close()
                self.violation_window = None
            
            # 移除菜单项
            if self.menu_action and hasattr(self.main_window, 'tools_menu'):
                self.main_window.tools_menu.removeAction(self.menu_action)
                self.menu_action = None
            
            print(f"插件 {self.name} 资源清理完成")
            
        except Exception as e:
            print(f"插件 {self.name} 资源清理失败: {str(e)}")


# 插件实例
plugin_instance = TimingViolationPlugin()
