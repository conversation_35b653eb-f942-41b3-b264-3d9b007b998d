"""
时序违例确认插件数据模型

包含数据库操作、数据结构定义等功能。
"""

import os
import sqlite3
import threading
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from PyQt5.QtCore import QObject, pyqtSignal


class ViolationDataModel(QObject):
    """时序违例数据模型"""
    
    # 信号定义
    violation_added = pyqtSignal(dict)      # 违例添加信号
    violation_updated = pyqtSignal(dict)    # 违例更新信号
    confirmation_updated = pyqtSignal(dict) # 确认更新信号
    data_loaded = pyqtSignal(list)          # 数据加载信号
    
    def __init__(self):
        super().__init__()
        self.db_path = self._get_database_path()
        self._lock = threading.Lock()
        self.init_database()

        # 查询缓存机制
        self.query_cache = {}
        self.cache_max_size = 100  # 最大缓存条目数
        self.cache_ttl = 300  # 缓存生存时间（秒）
        self.cache_hits = 0
        self.cache_misses = 0
    
    def _get_database_path(self) -> str:
        """获取数据库文件路径"""
        # 数据库位于VIOLATION_CHECK目录下
        base_dir = os.path.join(os.getcwd(), "VIOLATION_CHECK")
        os.makedirs(base_dir, exist_ok=True)
        return os.path.join(base_dir, "timing_violations.db")

    def _get_cache_key(self, query_type: str, **kwargs) -> str:
        """生成缓存键"""
        import hashlib
        key_data = f"{query_type}:{str(sorted(kwargs.items()))}"
        return hashlib.md5(key_data.encode()).hexdigest()

    def _get_from_cache(self, cache_key: str):
        """从缓存获取数据"""
        import time
        if cache_key in self.query_cache:
            cached_data, timestamp = self.query_cache[cache_key]
            if time.time() - timestamp < self.cache_ttl:
                self.cache_hits += 1
                return cached_data
            else:
                # 缓存过期，删除
                del self.query_cache[cache_key]

        self.cache_misses += 1
        return None

    def _set_cache(self, cache_key: str, data):
        """设置缓存数据"""
        import time

        # 如果缓存已满，删除最旧的条目
        if len(self.query_cache) >= self.cache_max_size:
            oldest_key = min(self.query_cache.keys(),
                           key=lambda k: self.query_cache[k][1])
            del self.query_cache[oldest_key]

        self.query_cache[cache_key] = (data, time.time())

    def clear_cache(self):
        """清空缓存"""
        self.query_cache.clear()
        self.cache_hits = 0
        self.cache_misses = 0

    def get_cache_stats(self) -> dict:
        """获取缓存统计信息"""
        total_requests = self.cache_hits + self.cache_misses
        hit_rate = (self.cache_hits / total_requests * 100) if total_requests > 0 else 0

        return {
            'cache_size': len(self.query_cache),
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'hit_rate': hit_rate,
            'max_size': self.cache_max_size
        }

    def init_database(self):
        """初始化数据库表结构"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                
                # 获取SQLite版本信息
                cursor.execute("SELECT sqlite_version()")
                sqlite_version = cursor.fetchone()[0]
                print(f"时序违例插件 - SQLite版本: {sqlite_version}")
                
                # 创建时序违例记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS timing_violations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        case_name TEXT NOT NULL,
                        corner TEXT,
                        num INTEGER NOT NULL,
                        hier TEXT NOT NULL,
                        time_fs INTEGER NOT NULL,
                        time_display TEXT NOT NULL,
                        check_info TEXT NOT NULL,
                        file_path TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(case_name, corner, num, hier, check_info)
                    )
                ''')
                
                # 创建确认记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS confirmation_records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        violation_id INTEGER NOT NULL,
                        status TEXT NOT NULL DEFAULT 'pending',
                        confirmer TEXT,
                        result TEXT,
                        reason TEXT,
                        is_auto_confirmed BOOLEAN DEFAULT 0,
                        confirmed_at TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (violation_id) REFERENCES timing_violations(id)
                    )
                ''')
                
                # 创建历史匹配表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS violation_patterns (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        hier_pattern TEXT NOT NULL,
                        check_pattern TEXT NOT NULL,
                        default_confirmer TEXT,
                        default_result TEXT,
                        default_reason TEXT,
                        match_count INTEGER DEFAULT 1,
                        last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(hier_pattern, check_pattern)
                    )
                ''')
                
                # 创建索引 - 优化查询性能
                index_statements = [
                    # 原有索引
                    'CREATE INDEX IF NOT EXISTS idx_violations_case_corner ON timing_violations(case_name, corner)',
                    'CREATE INDEX IF NOT EXISTS idx_violations_hier_check ON timing_violations(hier, check_info)',
                    'CREATE INDEX IF NOT EXISTS idx_confirmations_violation ON confirmation_records(violation_id)',
                    'CREATE INDEX IF NOT EXISTS idx_patterns_hier_check ON violation_patterns(hier_pattern, check_pattern)',

                    # 新增性能优化索引 - timing_violations 表
                    'CREATE INDEX IF NOT EXISTS idx_violations_time_fs ON timing_violations(time_fs)',
                    'CREATE INDEX IF NOT EXISTS idx_violations_case_time ON timing_violations(case_name, time_fs)',
                    'CREATE INDEX IF NOT EXISTS idx_violations_corner_time ON timing_violations(corner, time_fs)',
                    'CREATE INDEX IF NOT EXISTS idx_violations_composite ON timing_violations(case_name, corner, time_fs)',

                    # 确认记录优化索引 - confirmation_records 表
                    'CREATE INDEX IF NOT EXISTS idx_confirmations_status ON confirmation_records(status)',
                    'CREATE INDEX IF NOT EXISTS idx_confirmations_confirmer ON confirmation_records(confirmer)',
                    'CREATE INDEX IF NOT EXISTS idx_confirmations_result ON confirmation_records(result)',
                    'CREATE INDEX IF NOT EXISTS idx_confirmations_timestamp ON confirmation_records(confirmed_at)',
                    'CREATE INDEX IF NOT EXISTS idx_confirmations_status_time ON confirmation_records(status, confirmed_at)',
                ]
                
                for sql in index_statements:
                    try:
                        cursor.execute(sql)
                    except Exception as e:
                        print(f"创建索引失败: {e}")
                        # 索引创建失败不影响整体功能
                
                conn.commit()
                print("时序违例插件数据库初始化成功")
                
            except Exception as e:
                print(f"时序违例插件数据库初始化失败: {str(e)}")
                conn.rollback()
                raise
            finally:
                conn.close()
    
    def add_violations(self, violations: List[Dict], case_name: str, corner: str, file_path: str) -> int:
        """批量添加时序违例记录（优化版本）

        Args:
            violations: 违例列表
            case_name: 用例名称
            corner: Corner信息
            file_path: 日志文件路径

        Returns:
            int: 成功添加的记录数
        """
        if len(violations) > 1000:
            # 大数据量使用批量优化方法
            return self._add_violations_batch_optimized(violations, case_name, corner, file_path)
        else:
            # 小数据量使用原有方法
            return self._add_violations_original(violations, case_name, corner, file_path)

    def _add_violations_batch_optimized(self, violations: List[Dict], case_name: str, corner: str, file_path: str) -> int:
        """大数据量批量添加优化版本"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # 批量检查已存在的记录
                existing_keys = set()
                if violations:
                    # 构建查询条件
                    check_params = [(case_name, corner, v['NUM'], v['Hier'], v['Check']) for v in violations]
                    placeholders = ','.join(['(?,?,?,?,?)'] * len(check_params))
                    flat_params = [item for sublist in check_params for item in sublist]

                    cursor.execute(f'''
                        SELECT num, hier, check_info FROM timing_violations
                        WHERE (case_name, corner, num, hier, check_info) IN ({placeholders})
                    ''', flat_params)

                    existing_keys = {(row[0], row[1], row[2]) for row in cursor.fetchall()}

                # 准备批量插入数据
                violation_data = []
                confirmation_data = []
                new_violations = []

                for violation in violations:
                    key = (violation['NUM'], violation['Hier'], violation['Check'])
                    if key not in existing_keys:
                        violation_data.append((
                            case_name, corner, violation['NUM'], violation['Hier'],
                            violation['time_fs'], violation['Time'], violation['Check'],
                            file_path, now
                        ))
                        new_violations.append(violation)

                # 批量插入违例记录
                if violation_data:
                    cursor.executemany('''
                        INSERT INTO timing_violations
                        (case_name, corner, num, hier, time_fs, time_display, check_info, file_path, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', violation_data)

                    # 获取插入的记录ID
                    cursor.execute('''
                        SELECT id FROM timing_violations
                        WHERE case_name = ? AND corner = ? AND created_at = ?
                        ORDER BY id DESC LIMIT ?
                    ''', (case_name, corner, now, len(violation_data)))

                    violation_ids = [row[0] for row in cursor.fetchall()]

                    # 准备确认记录数据
                    confirmation_data = [(vid, 'pending', now, now) for vid in violation_ids]

                    # 批量插入确认记录
                    cursor.executemany('''
                        INSERT INTO confirmation_records
                        (violation_id, status, created_at, updated_at)
                        VALUES (?, ?, ?, ?)
                    ''', confirmation_data)

                conn.commit()
                success_count = len(violation_data)

                # 只发送一次批量完成信号，避免信号队列堆积
                if success_count > 0:
                    self.data_loaded.emit(new_violations)

                return success_count

            except Exception as e:
                conn.rollback()
                print(f"批量添加违例记录失败: {str(e)}")
                return 0
            finally:
                conn.close()

    def _add_violations_original(self, violations: List[Dict], case_name: str, corner: str, file_path: str) -> int:
        """原有的逐条添加方法（小数据量使用）"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                success_count = 0

                for violation in violations:
                    try:
                        # 检查记录是否已存在
                        cursor.execute('''
                            SELECT id FROM timing_violations
                            WHERE case_name = ? AND corner = ? AND num = ? AND hier = ? AND check_info = ?
                        ''', (case_name, corner, violation['NUM'], violation['Hier'], violation['Check']))

                        if cursor.fetchone():
                            continue  # 记录已存在，跳过

                        # 插入新记录
                        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        cursor.execute('''
                            INSERT INTO timing_violations
                            (case_name, corner, num, hier, time_fs, time_display, check_info, file_path, created_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            case_name, corner, violation['NUM'], violation['Hier'],
                            violation['time_fs'], violation['Time'], violation['Check'],
                            file_path, now
                        ))

                        violation_id = cursor.lastrowid

                        # 创建对应的确认记录
                        cursor.execute('''
                            INSERT INTO confirmation_records
                            (violation_id, status, created_at, updated_at)
                            VALUES (?, 'pending', ?, ?)
                        ''', (violation_id, now, now))

                        success_count += 1

                        # 发送信号
                        violation_data = {
                            'id': violation_id,
                            'case_name': case_name,
                            'corner': corner,
                            'num': violation['NUM'],
                            'hier': violation['Hier'],
                            'time_fs': violation['time_fs'],
                            'time_display': violation['Time'],
                            'check_info': violation['Check'],
                            'status': 'pending'
                        }
                        self.violation_added.emit(violation_data)

                    except Exception as e:
                        print(f"添加违例记录失败: {str(e)}")
                        continue

                conn.commit()
                return success_count

            except Exception as e:
                conn.rollback()
                print(f"批量添加违例记录失败: {str(e)}")
                return 0
            finally:
                conn.close()
    
    def get_violations_by_case(self, case_name: str, corner: str = None) -> List[Dict]:
        """获取指定用例的违例记录
        
        Args:
            case_name: 用例名称
            corner: Corner信息（可选）
            
        Returns:
            List[Dict]: 违例记录列表
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                
                if corner:
                    sql = '''
                        SELECT v.*, c.status, c.confirmer, c.result, c.reason, c.is_auto_confirmed, c.confirmed_at
                        FROM timing_violations v
                        LEFT JOIN confirmation_records c ON v.id = c.violation_id
                        WHERE v.case_name = ? AND v.corner = ?
                        ORDER BY v.num
                    '''
                    cursor.execute(sql, (case_name, corner))
                else:
                    sql = '''
                        SELECT v.*, c.status, c.confirmer, c.result, c.reason, c.is_auto_confirmed, c.confirmed_at
                        FROM timing_violations v
                        LEFT JOIN confirmation_records c ON v.id = c.violation_id
                        WHERE v.case_name = ?
                        ORDER BY v.num
                    '''
                    cursor.execute(sql, (case_name,))
                
                rows = cursor.fetchall()
                columns = [description[0] for description in cursor.description]
                
                violations = []
                for row in rows:
                    violation = dict(zip(columns, row))
                    violations.append(violation)
                
                return violations
                
            except Exception as e:
                print(f"获取违例记录失败: {str(e)}")
                return []
            finally:
                conn.close()

    def get_violation_by_id(self, violation_id: int) -> Dict:
        """根据ID获取单个违例记录

        Args:
            violation_id: 违例记录ID

        Returns:
            Dict: 违例记录，如果未找到返回None
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                # 查询违例记录及其确认信息
                cursor.execute('''
                    SELECT v.*, c.status, c.confirmer, c.result, c.reason, c.is_auto_confirmed, c.confirmed_at
                    FROM timing_violations v
                    LEFT JOIN confirmation_records c ON v.id = c.violation_id
                    WHERE v.id = ?
                ''', (violation_id,))

                row = cursor.fetchone()
                if not row:
                    return None

                # 构建违例记录字典
                violation = {
                    'id': row[0],
                    'case_name': row[1],
                    'corner': row[2],
                    'num': row[3],
                    'hier': row[4],
                    'time_fs': row[5],
                    'time_display': row[6],
                    'check_info': row[7],
                    'file_path': row[8],
                    'created_at': row[9],
                    'status': row[10] or 'pending',
                    'confirmer': row[11] or '',
                    'result': row[12] or '',
                    'reason': row[13] or '',
                    'is_auto_confirmed': bool(row[14]) if row[14] is not None else False,
                    'confirmed_at': row[15]
                }

                return violation

            except Exception as e:
                print(f"根据ID获取违例记录失败: {str(e)}")
                return None
            finally:
                conn.close()

    def update_confirmation(self, violation_id: int, status: str, confirmer: str = None,
                          result: str = None, reason: str = None, is_auto: bool = False) -> bool:
        """更新确认记录

        Args:
            violation_id: 违例记录ID
            status: 确认状态 (pending/confirmed/ignored)
            confirmer: 确认人
            result: 确认结果 (pass/issue)
            reason: 确认理由
            is_auto: 是否自动确认

        Returns:
            bool: 是否成功更新
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # 更新确认记录
                cursor.execute('''
                    UPDATE confirmation_records
                    SET status = ?, confirmer = ?, result = ?, reason = ?,
                        is_auto_confirmed = ?, confirmed_at = ?, updated_at = ?
                    WHERE violation_id = ?
                ''', (status, confirmer, result, reason, is_auto, now, now, violation_id))

                conn.commit()

                # 准备信号数据，但在锁外发送
                confirmation_data = {
                    'violation_id': violation_id,
                    'status': status,
                    'confirmer': confirmer,
                    'result': result,
                    'reason': reason,
                    'is_auto_confirmed': is_auto,
                    'confirmed_at': now
                }

                return True, confirmation_data

            except Exception as e:
                conn.rollback()
                print(f"更新确认记录失败: {str(e)}")
                return False, None
            finally:
                conn.close()

        # 在锁外发送信号，避免死锁
        if 'confirmation_data' in locals():
            try:
                self.confirmation_updated.emit(confirmation_data)
            except Exception as e:
                print(f"发送确认更新信号失败: {str(e)}")

    def auto_confirm_by_reset_time(self, case_name: str, corner: str, reset_time_ns: float) -> int:
        """根据复位时间自动确认违例

        Args:
            case_name: 用例名称
            corner: Corner信息
            reset_time_ns: 复位时间（纳秒）

        Returns:
            int: 自动确认的记录数
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                # 转换时间单位：纳秒 -> 飞秒
                reset_time_fs = int(reset_time_ns * 1000000)
                print(f"自动确认参数 - 用例: {case_name}, corner: {corner}, 复位时间: {reset_time_ns}ns ({reset_time_fs}fs)")

                # 查找需要自动确认的违例（时间小于等于复位时间且状态为pending）
                cursor.execute('''
                    SELECT v.id, v.num, v.hier, v.time_fs, v.time_display
                    FROM timing_violations v
                    LEFT JOIN confirmation_records c ON v.id = c.violation_id
                    WHERE v.case_name = ? AND v.corner = ? AND v.time_fs <= ?
                    AND (c.status = 'pending' OR c.status IS NULL)
                ''', (case_name, corner, reset_time_fs))

                violations_to_confirm = cursor.fetchall()
                print(f"找到 {len(violations_to_confirm)} 条需要自动确认的违例")

                if not violations_to_confirm:
                    print("没有找到符合条件的违例记录")
                    return 0

                # 批量更新确认记录
                now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                reason = f"复位期间时序违例（<= {reset_time_ns}ns），可以忽略"
                confirmed_count = 0

                for violation_row in violations_to_confirm:
                    violation_id, num, hier, time_fs, time_display = violation_row
                    time_ns = time_fs / 1000000  # 转换为纳秒显示

                    print(f"自动确认违例 - NUM: {num}, Hier: {hier}, Time: {time_display} ({time_ns:.3f}ns)")

                    # 检查是否已有确认记录
                    cursor.execute('SELECT id FROM confirmation_records WHERE violation_id = ?', (violation_id,))
                    existing_record = cursor.fetchone()

                    if existing_record:
                        # 更新现有确认记录
                        cursor.execute('''
                            UPDATE confirmation_records
                            SET status = 'confirmed', result = 'pass', reason = ?,
                                confirmer = '系统自动', is_auto_confirmed = 1,
                                confirmed_at = ?, updated_at = ?
                            WHERE violation_id = ?
                        ''', (reason, now, now, violation_id))
                    else:
                        # 创建新的确认记录
                        cursor.execute('''
                            INSERT INTO confirmation_records
                            (violation_id, status, result, reason, confirmer, is_auto_confirmed,
                             confirmed_at, created_at, updated_at)
                            VALUES (?, 'confirmed', 'pass', ?, '系统自动', 1, ?, ?, ?)
                        ''', (violation_id, reason, now, now, now))

                    confirmed_count += 1

                conn.commit()
                print(f"成功自动确认 {confirmed_count} 条违例记录")
                return confirmed_count

            except Exception as e:
                conn.rollback()
                print(f"自动确认失败: {str(e)}")
                import traceback
                traceback.print_exc()
                return 0
            finally:
                conn.close()

    def save_pattern(self, hier: str, check: str, confirmer: str, result: str, reason: str):
        """保存确认模式到历史记录

        Args:
            hier: 层级路径
            check: 检查信息
            confirmer: 确认人
            result: 确认结果
            reason: 确认理由
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # 检查模式是否已存在
                cursor.execute('''
                    SELECT id, match_count FROM violation_patterns
                    WHERE hier_pattern = ? AND check_pattern = ?
                ''', (hier, check))

                existing = cursor.fetchone()

                if existing:
                    # 更新现有模式
                    pattern_id, match_count = existing
                    cursor.execute('''
                        UPDATE violation_patterns
                        SET default_confirmer = ?, default_result = ?, default_reason = ?,
                            match_count = ?, last_used = ?
                        WHERE id = ?
                    ''', (confirmer, result, reason, match_count + 1, now, pattern_id))
                else:
                    # 插入新模式
                    cursor.execute('''
                        INSERT INTO violation_patterns
                        (hier_pattern, check_pattern, default_confirmer, default_result,
                         default_reason, match_count, last_used)
                        VALUES (?, ?, ?, ?, ?, 1, ?)
                    ''', (hier, check, confirmer, result, reason, now))

                conn.commit()

            except Exception as e:
                conn.rollback()
                print(f"保存确认模式失败: {str(e)}")
            finally:
                conn.close()

    def get_pattern_suggestions(self, hier: str, check: str) -> Optional[Dict]:
        """获取历史模式建议

        Args:
            hier: 层级路径
            check: 检查信息

        Returns:
            Optional[Dict]: 建议信息，如果没有匹配返回None
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                # 精确匹配
                cursor.execute('''
                    SELECT default_confirmer, default_result, default_reason, match_count
                    FROM violation_patterns
                    WHERE hier_pattern = ? AND check_pattern = ?
                    ORDER BY last_used DESC
                    LIMIT 1
                ''', (hier, check))

                result = cursor.fetchone()
                if result:
                    return {
                        'confirmer': result[0],
                        'result': result[1],
                        'reason': result[2],
                        'match_count': result[3]
                    }

                return None

            except Exception as e:
                print(f"获取模式建议失败: {str(e)}")
                return None
            finally:
                conn.close()

    def apply_historical_confirmations(self, case_name: str, corner: str) -> int:
        """自动应用历史确认记录（优化版本）

        Args:
            case_name: 用例名称
            corner: Corner信息

        Returns:
            int: 自动应用的确认记录数
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                # 获取当前用例的待确认违例
                cursor.execute('''
                    SELECT v.id, v.hier, v.check_info
                    FROM timing_violations v
                    JOIN confirmation_records c ON v.id = c.violation_id
                    WHERE v.case_name = ? AND v.corner = ? AND c.status = 'pending'
                ''', (case_name, corner))

                pending_violations = cursor.fetchall()

                if not pending_violations:
                    return 0

                # 大数据量使用批量优化方法
                if len(pending_violations) > 500:
                    return self._apply_historical_confirmations_batch(cursor, pending_violations, conn)
                else:
                    return self._apply_historical_confirmations_original(cursor, pending_violations, conn)

            except Exception as e:
                conn.rollback()
                print(f"应用历史确认失败: {str(e)}")
                return 0
            finally:
                conn.close()

    def _apply_historical_confirmations_batch(self, cursor, pending_violations, conn) -> int:
        """批量处理历史确认（大数据量优化）"""
        try:
            # 一次性获取所有历史模式
            cursor.execute('''
                SELECT hier_pattern, check_pattern, default_confirmer,
                       default_result, default_reason, match_count
                FROM violation_patterns
                ORDER BY last_used DESC
            ''')

            all_patterns = cursor.fetchall()
            pattern_dict = {}
            for pattern in all_patterns:
                key = (pattern[0], pattern[1])  # (hier_pattern, check_pattern)
                pattern_dict[key] = {
                    'confirmer': pattern[2],
                    'result': pattern[3],
                    'reason': pattern[4],
                    'match_count': pattern[5]
                }

            # 在内存中进行模式匹配
            matches = []
            pattern_updates = []
            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            for violation_id, hier, check_info in pending_violations:
                key = (hier, check_info)
                if key in pattern_dict:
                    pattern = pattern_dict[key]
                    matches.append((
                        'confirmed', pattern['confirmer'], pattern['result'],
                        pattern['reason'], 0, now, now, violation_id
                    ))
                    pattern_updates.append((
                        pattern['match_count'] + 1, now, hier, check_info
                    ))

            # 批量更新确认记录
            if matches:
                cursor.executemany('''
                    UPDATE confirmation_records
                    SET status = ?, confirmer = ?, result = ?, reason = ?,
                        is_auto_confirmed = ?, confirmed_at = ?, updated_at = ?
                    WHERE violation_id = ?
                ''', matches)

                # 批量更新模式使用次数
                cursor.executemany('''
                    UPDATE violation_patterns
                    SET match_count = ?, last_used = ?
                    WHERE hier_pattern = ? AND check_pattern = ?
                ''', pattern_updates)

            conn.commit()
            applied_count = len(matches)
            print(f"批量应用历史确认: {applied_count} 条记录")
            return applied_count

        except Exception as e:
            print(f"批量应用历史确认失败: {str(e)}")
            return 0

    def _apply_historical_confirmations_original(self, cursor, pending_violations, conn) -> int:
        """原有的逐条处理方法（小数据量使用）"""
        try:
            applied_count = 0
            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            for violation_id, hier, check_info in pending_violations:
                # 查找匹配的历史模式
                cursor.execute('''
                    SELECT default_confirmer, default_result, default_reason, match_count
                    FROM violation_patterns
                    WHERE hier_pattern = ? AND check_pattern = ?
                    ORDER BY last_used DESC
                    LIMIT 1
                ''', (hier, check_info))

                pattern = cursor.fetchone()

                if pattern:
                    confirmer, result, reason, match_count = pattern

                    # 应用历史确认信息
                    cursor.execute('''
                        UPDATE confirmation_records
                        SET status = 'confirmed', confirmer = ?, result = ?, reason = ?,
                            is_auto_confirmed = 0, confirmed_at = ?, updated_at = ?
                        WHERE violation_id = ?
                    ''', (confirmer, result, reason, now, now, violation_id))

                    # 更新模式使用次数
                    cursor.execute('''
                        UPDATE violation_patterns
                        SET match_count = ?, last_used = ?
                        WHERE hier_pattern = ? AND check_pattern = ?
                    ''', (match_count + 1, now, hier, check_info))

                    applied_count += 1

            conn.commit()
            return applied_count

        except Exception as e:
            print(f"应用历史确认失败: {str(e)}")
            return 0

    def get_all_patterns(self) -> List[Dict]:
        """获取所有历史确认模式

        Returns:
            List[Dict]: 历史模式列表
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT hier_pattern, check_pattern, default_confirmer,
                           default_result, default_reason, match_count, last_used
                    FROM violation_patterns
                    ORDER BY last_used DESC
                ''')

                rows = cursor.fetchall()
                patterns = []

                for row in rows:
                    pattern = {
                        'hier_pattern': row[0],
                        'check_pattern': row[1],
                        'default_confirmer': row[2],
                        'default_result': row[3],
                        'default_reason': row[4],
                        'match_count': row[5],
                        'last_used': row[6]
                    }
                    patterns.append(pattern)

                return patterns

            except Exception as e:
                print(f"获取历史模式失败: {str(e)}")
                return []
            finally:
                conn.close()

    def clear_all_patterns(self) -> bool:
        """清除所有历史确认模式

        Returns:
            bool: 是否成功清除
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM violation_patterns')
                conn.commit()
                print("已清除所有历史确认模式")
                return True

            except Exception as e:
                conn.rollback()
                print(f"清除历史模式失败: {str(e)}")
                return False
            finally:
                conn.close()

    def update_case_corner(self, case_name: str, old_corner: str, new_corner: str) -> bool:
        """更新用例的corner信息

        Args:
            case_name: 用例名称
            old_corner: 原corner
            new_corner: 新corner

        Returns:
            bool: 是否成功更新
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                # 更新违例记录的corner
                cursor.execute('''
                    UPDATE timing_violations
                    SET corner = ?
                    WHERE case_name = ? AND corner = ?
                ''', (new_corner, case_name, old_corner))

                updated_count = cursor.rowcount
                conn.commit()

                print(f"更新corner: {case_name} 从 '{old_corner}' 到 '{new_corner}', 影响 {updated_count} 条记录")
                return updated_count > 0

            except Exception as e:
                conn.rollback()
                print(f"更新corner失败: {str(e)}")
                return False
            finally:
                conn.close()

    def clear_case_data(self, case_name: str, corner: str = None):
        """清除指定用例的数据

        Args:
            case_name: 用例名称
            corner: Corner信息（可选）
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                if corner:
                    # 删除确认记录
                    cursor.execute('''
                        DELETE FROM confirmation_records
                        WHERE violation_id IN (
                            SELECT id FROM timing_violations
                            WHERE case_name = ? AND corner = ?
                        )
                    ''', (case_name, corner))

                    # 删除违例记录
                    cursor.execute('''
                        DELETE FROM timing_violations
                        WHERE case_name = ? AND corner = ?
                    ''', (case_name, corner))
                else:
                    # 删除确认记录
                    cursor.execute('''
                        DELETE FROM confirmation_records
                        WHERE violation_id IN (
                            SELECT id FROM timing_violations
                            WHERE case_name = ?
                        )
                    ''', (case_name,))

                    # 删除违例记录
                    cursor.execute('''
                        DELETE FROM timing_violations
                        WHERE case_name = ?
                    ''', (case_name,))

                conn.commit()
                print(f"已清除用例 {case_name} 的数据")

            except Exception as e:
                conn.rollback()
                print(f"清除用例数据失败: {str(e)}")
            finally:
                conn.close()
