"""
时序违例插件性能优化配置

提供性能参数配置、自动调优和性能预设功能。
"""

import json
import os
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict


@dataclass
class PerformanceConfig:
    """性能配置数据类"""
    
    # UI渲染配置
    page_size: int = 50
    render_batch_size: int = 10
    lazy_render_threshold: int = 20
    performance_threshold: int = 500
    
    # 内存管理配置
    max_pool_size: int = 500
    memory_cleanup_interval: int = 50
    cache_max_size: int = 100
    cache_ttl: int = 300
    
    # 性能阈值配置
    ui_response_time_threshold: float = 0.1
    memory_warning_threshold: float = 200.0
    memory_critical_threshold: float = 500.0
    cpu_warning_threshold: float = 50.0
    render_time_threshold: float = 0.05
    query_time_threshold: float = 0.1
    
    # 自动优化配置
    auto_optimization_enabled: bool = True
    adaptive_threshold_enabled: bool = True
    emergency_cleanup_enabled: bool = True
    performance_monitoring_enabled: bool = True
    
    # 高级配置
    virtual_scroll_enabled: bool = True
    async_rendering_enabled: bool = True
    database_connection_pool_size: int = 5
    query_cache_enabled: bool = True


class PerformanceConfigManager:
    """性能配置管理器"""
    
    def __init__(self, config_dir: Optional[str] = None):
        if config_dir is None:
            config_dir = os.path.join(os.getcwd(), "VIOLATION_CHECK")
        
        self.config_dir = config_dir
        self.config_file = os.path.join(config_dir, "performance_config.json")
        self.config = PerformanceConfig()
        
        # 确保配置目录存在
        os.makedirs(config_dir, exist_ok=True)
        
        # 加载配置
        self.load_config()
    
    def load_config(self) -> bool:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 更新配置对象
                for key, value in config_data.items():
                    if hasattr(self.config, key):
                        setattr(self.config, key, value)
                
                print(f"性能配置加载成功: {self.config_file}")
                return True
            else:
                # 配置文件不存在，使用默认配置并保存
                self.save_config()
                print("使用默认性能配置")
                return True
                
        except Exception as e:
            print(f"加载性能配置失败: {e}")
            return False
    
    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            config_data = asdict(self.config)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            print(f"性能配置保存成功: {self.config_file}")
            return True
            
        except Exception as e:
            print(f"保存性能配置失败: {e}")
            return False
    
    def get_config(self) -> PerformanceConfig:
        """获取当前配置"""
        return self.config
    
    def update_config(self, **kwargs) -> bool:
        """更新配置参数"""
        try:
            for key, value in kwargs.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)
                else:
                    print(f"警告: 未知的配置参数 {key}")
            
            return self.save_config()
            
        except Exception as e:
            print(f"更新配置失败: {e}")
            return False
    
    def reset_to_defaults(self) -> bool:
        """重置为默认配置"""
        try:
            self.config = PerformanceConfig()
            return self.save_config()
        except Exception as e:
            print(f"重置配置失败: {e}")
            return False
    
    def apply_preset(self, preset_name: str) -> bool:
        """应用性能预设"""
        presets = self.get_performance_presets()
        
        if preset_name not in presets:
            print(f"未知的性能预设: {preset_name}")
            return False
        
        try:
            preset_config = presets[preset_name]
            return self.update_config(**preset_config)
        except Exception as e:
            print(f"应用性能预设失败: {e}")
            return False
    
    def get_performance_presets(self) -> Dict[str, Dict[str, Any]]:
        """获取性能预设配置"""
        return {
            "高性能": {
                "page_size": 30,
                "render_batch_size": 5,
                "performance_threshold": 300,
                "max_pool_size": 300,
                "memory_cleanup_interval": 30,
                "cache_max_size": 50,
                "ui_response_time_threshold": 0.05,
                "memory_warning_threshold": 150.0,
                "auto_optimization_enabled": True,
                "adaptive_threshold_enabled": True,
            },
            
            "平衡": {
                "page_size": 50,
                "render_batch_size": 10,
                "performance_threshold": 500,
                "max_pool_size": 500,
                "memory_cleanup_interval": 50,
                "cache_max_size": 100,
                "ui_response_time_threshold": 0.1,
                "memory_warning_threshold": 200.0,
                "auto_optimization_enabled": True,
                "adaptive_threshold_enabled": True,
            },
            
            "高容量": {
                "page_size": 100,
                "render_batch_size": 20,
                "performance_threshold": 1000,
                "max_pool_size": 1000,
                "memory_cleanup_interval": 100,
                "cache_max_size": 200,
                "ui_response_time_threshold": 0.2,
                "memory_warning_threshold": 400.0,
                "auto_optimization_enabled": False,
                "adaptive_threshold_enabled": False,
            },
            
            "低内存": {
                "page_size": 20,
                "render_batch_size": 5,
                "performance_threshold": 200,
                "max_pool_size": 100,
                "memory_cleanup_interval": 20,
                "cache_max_size": 30,
                "ui_response_time_threshold": 0.15,
                "memory_warning_threshold": 100.0,
                "memory_critical_threshold": 200.0,
                "auto_optimization_enabled": True,
                "adaptive_threshold_enabled": True,
                "emergency_cleanup_enabled": True,
            }
        }
    
    def auto_detect_optimal_config(self, system_info: Optional[Dict] = None) -> str:
        """自动检测最优配置"""
        try:
            # 获取系统信息
            if system_info is None:
                try:
                    import psutil
                    system_info = {
                        'memory_gb': psutil.virtual_memory().total / (1024**3),
                        'cpu_count': psutil.cpu_count(),
                        'cpu_freq': psutil.cpu_freq().current if psutil.cpu_freq() else 2000,
                    }
                except ImportError:
                    # psutil 不可用时使用默认值
                    import os
                    system_info = {
                        'memory_gb': 8,  # 默认8GB
                        'cpu_count': os.cpu_count() or 4,  # 使用 os.cpu_count()
                        'cpu_freq': 2000,
                    }

            memory_gb = system_info.get('memory_gb', 8)
            cpu_count = system_info.get('cpu_count', 4)

            # 基于系统配置选择预设
            if memory_gb >= 16 and cpu_count >= 8:
                return "高容量"
            elif memory_gb >= 8 and cpu_count >= 4:
                return "平衡"
            elif memory_gb >= 4:
                return "高性能"
            else:
                return "低内存"

        except Exception as e:
            print(f"自动检测配置失败: {e}")
            return "平衡"
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            "当前预设": self._detect_current_preset(),
            "页面大小": self.config.page_size,
            "性能阈值": self.config.performance_threshold,
            "内存池大小": self.config.max_pool_size,
            "缓存大小": self.config.cache_max_size,
            "自动优化": "启用" if self.config.auto_optimization_enabled else "禁用",
            "自适应阈值": "启用" if self.config.adaptive_threshold_enabled else "禁用",
            "性能监控": "启用" if self.config.performance_monitoring_enabled else "禁用",
        }
    
    def _detect_current_preset(self) -> str:
        """检测当前配置对应的预设"""
        presets = self.get_performance_presets()
        current_config = asdict(self.config)
        
        for preset_name, preset_config in presets.items():
            match_count = 0
            total_keys = len(preset_config)
            
            for key, value in preset_config.items():
                if current_config.get(key) == value:
                    match_count += 1
            
            # 如果80%以上的配置匹配，认为是该预设
            if match_count / total_keys >= 0.8:
                return preset_name
        
        return "自定义"
    
    def validate_config(self) -> List[str]:
        """验证配置有效性"""
        warnings = []
        
        # 检查页面大小
        if self.config.page_size < 10:
            warnings.append("页面大小过小，可能影响用户体验")
        elif self.config.page_size > 200:
            warnings.append("页面大小过大，可能影响性能")
        
        # 检查内存配置
        if self.config.max_pool_size < 50:
            warnings.append("对象池大小过小，可能频繁创建控件")
        elif self.config.max_pool_size > 2000:
            warnings.append("对象池大小过大，可能占用过多内存")
        
        # 检查阈值配置
        if self.config.performance_threshold < 100:
            warnings.append("性能阈值过低，可能过早切换高性能模式")
        
        # 检查缓存配置
        if self.config.cache_max_size < 10:
            warnings.append("缓存大小过小，可能影响查询性能")
        
        return warnings


# 全局配置管理器实例
_config_manager = None

def get_performance_config() -> PerformanceConfigManager:
    """获取全局性能配置管理器"""
    global _config_manager
    if _config_manager is None:
        _config_manager = PerformanceConfigManager()
    return _config_manager
