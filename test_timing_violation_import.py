#!/usr/bin/env python3
"""
简单测试时序违例插件导入
"""

def test_import():
    """测试导入"""
    try:
        print("测试 QThread 导入...")
        from PyQt5.QtCore import QThread
        print("✓ QThread 导入成功")
        
        print("测试时序违例主窗口导入...")
        from plugins.user.timing_violation.main_window import AsyncDataProcessor
        print("✓ AsyncDataProcessor 导入成功")
        print(f"✓ AsyncDataProcessor 基类: {AsyncDataProcessor.__bases__}")
        
        print("测试完整主窗口导入...")
        from plugins.user.timing_violation.main_window import TimingViolationWindow
        print("✓ TimingViolationWindow 导入成功")
        
        print("\n🎉 所有导入测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_import()
