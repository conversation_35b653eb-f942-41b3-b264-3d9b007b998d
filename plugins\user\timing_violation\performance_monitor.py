"""
时序违例插件性能监控模块

提供实时性能监控、自适应优化和性能建议功能。
"""

import time
import threading
from typing import Dict, List, Optional, Tuple
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from PyQt5.QtWidgets import QApplication

# 安全导入 psutil
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("警告: psutil 不可用，部分性能监控功能将被禁用")


class PerformanceMonitor(QObject):
    """性能监控器"""
    
    # 信号定义
    performance_warning = pyqtSignal(str, dict)  # 警告类型, 详细信息
    optimization_suggested = pyqtSignal(str, list)  # 优化类型, 建议列表
    memory_threshold_exceeded = pyqtSignal(float)  # 内存使用超标
    
    def __init__(self):
        super().__init__()
        
        # 性能阈值配置
        self.thresholds = {
            'ui_response_time': 0.1,      # UI响应时间阈值（秒）
            'memory_warning': 200,         # 内存警告阈值（MB）
            'memory_critical': 500,        # 内存严重阈值（MB）
            'cpu_warning': 50,             # CPU警告阈值（%）
            'render_time': 0.05,           # 渲染时间阈值（秒）
            'query_time': 0.1,             # 查询时间阈值（秒）
        }
        
        # 性能统计
        self.stats = {
            'ui_operations': [],           # UI操作时间记录
            'memory_usage': [],            # 内存使用记录
            'cpu_usage': [],               # CPU使用记录
            'query_times': [],             # 查询时间记录
            'render_times': [],            # 渲染时间记录
            'cache_hit_rates': [],         # 缓存命中率记录
        }
        
        # 监控状态
        self.monitoring_enabled = True
        self.auto_optimization = True
        self.last_optimization_time = 0
        self.optimization_interval = 30  # 优化间隔（秒）
        
        # 性能计数器
        self.operation_count = 0
        self.warning_count = 0
        self.optimization_count = 0
        
        # 启动监控定时器
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self._monitor_system_performance)
        self.monitor_timer.start(5000)  # 每5秒监控一次
    
    def start_operation(self, operation_name: str) -> str:
        """开始监控操作"""
        operation_id = f"{operation_name}_{int(time.time() * 1000)}"
        self.stats[f'start_{operation_id}'] = time.time()
        return operation_id
    
    def end_operation(self, operation_id: str, operation_type: str = 'ui'):
        """结束监控操作"""
        start_key = f'start_{operation_id}'
        if start_key in self.stats:
            duration = time.time() - self.stats[start_key]
            del self.stats[start_key]
            
            # 记录操作时间
            if operation_type == 'ui':
                self.stats['ui_operations'].append(duration)
                self._check_ui_performance(duration)
            elif operation_type == 'query':
                self.stats['query_times'].append(duration)
                self._check_query_performance(duration)
            elif operation_type == 'render':
                self.stats['render_times'].append(duration)
                self._check_render_performance(duration)
            
            # 限制记录数量
            self._limit_stats_size()
            
            self.operation_count += 1
            
            # 定期触发优化检查
            if (time.time() - self.last_optimization_time > self.optimization_interval and 
                self.auto_optimization):
                self._check_optimization_opportunities()
    
    def record_memory_usage(self, memory_mb: float):
        """记录内存使用"""
        self.stats['memory_usage'].append(memory_mb)
        self._check_memory_usage(memory_mb)
        self._limit_stats_size()
    
    def record_cache_performance(self, hit_rate: float):
        """记录缓存性能"""
        self.stats['cache_hit_rates'].append(hit_rate)
        self._limit_stats_size()
    
    def _monitor_system_performance(self):
        """监控系统性能"""
        if not self.monitoring_enabled:
            return

        try:
            if PSUTIL_AVAILABLE:
                # 获取当前进程信息
                process = psutil.Process()

                # 内存使用
                memory_info = process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                self.record_memory_usage(memory_mb)

                # CPU使用
                cpu_percent = process.cpu_percent()
                self.stats['cpu_usage'].append(cpu_percent)

                if cpu_percent > self.thresholds['cpu_warning']:
                    self.performance_warning.emit('cpu_high', {
                        'cpu_percent': cpu_percent,
                        'threshold': self.thresholds['cpu_warning']
                    })
            else:
                # 使用简化的内存监控
                try:
                    import os
                    import gc

                    # 简单的内存估算
                    gc.collect()
                    memory_mb = 100  # 默认估算值
                    self.record_memory_usage(memory_mb)
                except Exception:
                    pass

            self._limit_stats_size()

        except Exception as e:
            print(f"系统性能监控失败: {e}")
    
    def _check_ui_performance(self, duration: float):
        """检查UI性能"""
        if duration > self.thresholds['ui_response_time']:
            self.performance_warning.emit('ui_slow', {
                'duration': duration,
                'threshold': self.thresholds['ui_response_time']
            })
            self.warning_count += 1
    
    def _check_query_performance(self, duration: float):
        """检查查询性能"""
        if duration > self.thresholds['query_time']:
            self.performance_warning.emit('query_slow', {
                'duration': duration,
                'threshold': self.thresholds['query_time']
            })
    
    def _check_render_performance(self, duration: float):
        """检查渲染性能"""
        if duration > self.thresholds['render_time']:
            self.performance_warning.emit('render_slow', {
                'duration': duration,
                'threshold': self.thresholds['render_time']
            })
    
    def _check_memory_usage(self, memory_mb: float):
        """检查内存使用"""
        if memory_mb > self.thresholds['memory_critical']:
            self.memory_threshold_exceeded.emit(memory_mb)
            self.performance_warning.emit('memory_critical', {
                'memory_mb': memory_mb,
                'threshold': self.thresholds['memory_critical']
            })
        elif memory_mb > self.thresholds['memory_warning']:
            self.performance_warning.emit('memory_warning', {
                'memory_mb': memory_mb,
                'threshold': self.thresholds['memory_warning']
            })
    
    def _check_optimization_opportunities(self):
        """检查优化机会"""
        self.last_optimization_time = time.time()
        suggestions = []
        
        # 分析UI性能
        if self.stats['ui_operations']:
            avg_ui_time = sum(self.stats['ui_operations'][-10:]) / min(10, len(self.stats['ui_operations']))
            if avg_ui_time > self.thresholds['ui_response_time']:
                suggestions.append("建议减少页面大小或启用分页")
                suggestions.append("考虑使用虚拟滚动")
        
        # 分析内存使用
        if self.stats['memory_usage']:
            avg_memory = sum(self.stats['memory_usage'][-5:]) / min(5, len(self.stats['memory_usage']))
            if avg_memory > self.thresholds['memory_warning']:
                suggestions.append("建议启用内存优化模式")
                suggestions.append("考虑增加对象池清理频率")
        
        # 分析缓存性能
        if self.stats['cache_hit_rates']:
            avg_hit_rate = sum(self.stats['cache_hit_rates'][-5:]) / min(5, len(self.stats['cache_hit_rates']))
            if avg_hit_rate < 50:  # 命中率低于50%
                suggestions.append("建议调整缓存策略")
                suggestions.append("考虑增加缓存大小")
        
        if suggestions:
            self.optimization_suggested.emit('performance', suggestions)
            self.optimization_count += 1
    
    def _limit_stats_size(self):
        """限制统计数据大小"""
        max_size = 100
        for key in ['ui_operations', 'memory_usage', 'cpu_usage', 
                   'query_times', 'render_times', 'cache_hit_rates']:
            if key in self.stats and len(self.stats[key]) > max_size:
                self.stats[key] = self.stats[key][-max_size:]
    
    def get_performance_summary(self) -> Dict:
        """获取性能摘要"""
        summary = {
            'operation_count': self.operation_count,
            'warning_count': self.warning_count,
            'optimization_count': self.optimization_count,
            'monitoring_enabled': self.monitoring_enabled,
        }
        
        # 计算平均值
        if self.stats['ui_operations']:
            summary['avg_ui_time'] = sum(self.stats['ui_operations']) / len(self.stats['ui_operations'])
        
        if self.stats['memory_usage']:
            summary['avg_memory_mb'] = sum(self.stats['memory_usage']) / len(self.stats['memory_usage'])
            summary['peak_memory_mb'] = max(self.stats['memory_usage'])
        
        if self.stats['cache_hit_rates']:
            summary['avg_cache_hit_rate'] = sum(self.stats['cache_hit_rates']) / len(self.stats['cache_hit_rates'])
        
        return summary
    
    def reset_stats(self):
        """重置统计数据"""
        for key in ['ui_operations', 'memory_usage', 'cpu_usage', 
                   'query_times', 'render_times', 'cache_hit_rates']:
            self.stats[key] = []
        
        self.operation_count = 0
        self.warning_count = 0
        self.optimization_count = 0
    
    def set_monitoring_enabled(self, enabled: bool):
        """设置监控状态"""
        self.monitoring_enabled = enabled
        if enabled:
            self.monitor_timer.start(5000)
        else:
            self.monitor_timer.stop()
    
    def set_auto_optimization(self, enabled: bool):
        """设置自动优化"""
        self.auto_optimization = enabled
    
    def update_thresholds(self, new_thresholds: Dict):
        """更新性能阈值"""
        self.thresholds.update(new_thresholds)


class AdaptiveOptimizer:
    """自适应优化器"""
    
    def __init__(self, performance_monitor: PerformanceMonitor):
        self.monitor = performance_monitor
        self.optimization_history = []
        
    def suggest_page_size(self, current_size: int, performance_data: Dict) -> int:
        """建议页面大小"""
        avg_render_time = performance_data.get('avg_render_time', 0)
        memory_usage = performance_data.get('memory_mb', 0)
        
        if avg_render_time > 0.1 or memory_usage > 300:
            # 性能不佳，减少页面大小
            return max(20, current_size - 10)
        elif avg_render_time < 0.05 and memory_usage < 150:
            # 性能良好，可以增加页面大小
            return min(100, current_size + 10)
        
        return current_size
    
    def suggest_cache_size(self, current_size: int, hit_rate: float) -> int:
        """建议缓存大小"""
        if hit_rate < 30:
            # 命中率低，增加缓存
            return min(200, current_size + 20)
        elif hit_rate > 80:
            # 命中率高，可以适当减少缓存
            return max(50, current_size - 10)
        
        return current_size
    
    def get_optimization_recommendations(self, current_config: Dict) -> List[str]:
        """获取优化建议"""
        recommendations = []
        performance_summary = self.monitor.get_performance_summary()
        
        # 基于性能数据的建议
        if performance_summary.get('avg_ui_time', 0) > 0.1:
            recommendations.append("UI响应较慢，建议减少页面大小")
        
        if performance_summary.get('avg_memory_mb', 0) > 200:
            recommendations.append("内存使用较高，建议启用内存优化")
        
        if performance_summary.get('warning_count', 0) > 10:
            recommendations.append("性能警告较多，建议检查系统配置")
        
        return recommendations
