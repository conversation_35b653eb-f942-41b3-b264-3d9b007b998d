"""
时序违例插件性能优化器

提供性能监控、分析和优化建议功能。
"""

import os
import time
import psutil
from typing import Dict, List, Optional, Tuple
from PyQt5.QtCore import QObject, pyqtSignal, QTimer


class PerformanceOptimizer(QObject):
    """性能优化器"""
    
    # 信号定义
    performance_warning = pyqtSignal(str, str)  # 警告类型, 警告信息
    optimization_suggestion = pyqtSignal(str)   # 优化建议
    
    def __init__(self):
        super().__init__()
        
        # 性能阈值配置
        self.thresholds = {
            'load_time_warning': 3.0,      # 加载时间警告阈值（秒）
            'load_time_critical': 10.0,    # 加载时间严重阈值（秒）
            'memory_warning': 300,         # 内存使用警告阈值（MB）
            'memory_critical': 500,        # 内存使用严重阈值（MB）
            'record_count_high': 5000,     # 高记录数阈值
            'record_count_critical': 15000, # 严重记录数阈值
            'ui_response_warning': 0.1,    # UI响应时间警告阈值（秒）
        }
        
        # 性能统计
        self.stats = {
            'total_load_time': 0,
            'total_records_processed': 0,
            'peak_memory_usage': 0,
            'ui_freeze_count': 0,
            'optimization_applied': []
        }
        
        # 性能监控定时器
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self._monitor_performance)
        self.monitor_timer.start(5000)  # 每5秒监控一次
        
    def analyze_file_performance(self, file_path: str) -> Dict:
        """分析文件性能特征
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict: 性能分析结果
        """
        try:
            file_size = os.path.getsize(file_path)
            file_size_mb = file_size / (1024 * 1024)
            
            # 估算记录数（基于经验公式）
            estimated_records = self._estimate_record_count(file_size)
            
            # 预测加载时间
            predicted_load_time = self._predict_load_time(file_size_mb, estimated_records)
            
            # 推荐解析策略
            recommended_strategy = self._recommend_parsing_strategy(file_size_mb, estimated_records)
            
            # 推荐显示模式
            recommended_display = self._recommend_display_mode(estimated_records)
            
            return {
                'file_size_mb': file_size_mb,
                'estimated_records': estimated_records,
                'predicted_load_time': predicted_load_time,
                'recommended_strategy': recommended_strategy,
                'recommended_display': recommended_display,
                'performance_level': self._assess_performance_level(file_size_mb, estimated_records)
            }
            
        except Exception as e:
            print(f"文件性能分析失败: {str(e)}")
            return {}
    
    def _estimate_record_count(self, file_size: int) -> int:
        """估算记录数量
        
        基于经验数据：平均每条违例记录约占用200-300字节
        """
        avg_bytes_per_record = 250
        return int(file_size / avg_bytes_per_record)
    
    def _predict_load_time(self, file_size_mb: float, record_count: int) -> float:
        """预测加载时间
        
        基于经验公式和硬件性能
        """
        # 基础时间（文件I/O）
        base_time = file_size_mb * 0.1  # 每MB约0.1秒
        
        # 解析时间（CPU密集型）
        parse_time = record_count * 0.0001  # 每条记录约0.0001秒
        
        # UI渲染时间（如果使用标准表格）
        ui_time = min(record_count * 0.0005, 5.0)  # 每条记录约0.0005秒，最多5秒
        
        return base_time + parse_time + ui_time
    
    def _recommend_parsing_strategy(self, file_size_mb: float, record_count: int) -> str:
        """推荐解析策略"""
        if file_size_mb > 50 or record_count > 20000:
            return "high_performance_streaming"
        elif file_size_mb > 10 or record_count > 5000:
            return "high_performance_async"
        else:
            return "standard_async"
    
    def _recommend_display_mode(self, record_count: int) -> str:
        """推荐显示模式"""
        if record_count > 1000:
            return "high_performance_table"
        else:
            return "standard_table"
    
    def _assess_performance_level(self, file_size_mb: float, record_count: int) -> str:
        """评估性能等级"""
        if file_size_mb > 100 or record_count > 50000:
            return "critical"
        elif file_size_mb > 20 or record_count > 10000:
            return "warning"
        elif file_size_mb > 5 or record_count > 2000:
            return "moderate"
        else:
            return "good"
    
    def monitor_load_performance(self, start_time: float, end_time: float, 
                               record_count: int, memory_usage: float) -> Dict:
        """监控加载性能
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            record_count: 记录数量
            memory_usage: 内存使用量（MB）
            
        Returns:
            Dict: 性能监控结果
        """
        load_time = end_time - start_time
        
        # 更新统计信息
        self.stats['total_load_time'] += load_time
        self.stats['total_records_processed'] += record_count
        self.stats['peak_memory_usage'] = max(self.stats['peak_memory_usage'], memory_usage)
        
        # 性能评估
        performance_issues = []
        optimization_suggestions = []
        
        # 加载时间检查
        if load_time > self.thresholds['load_time_critical']:
            performance_issues.append(f"加载时间过长: {load_time:.2f}秒")
            optimization_suggestions.extend([
                "建议使用高性能解析器",
                "考虑分批处理大文件",
                "启用流式处理模式"
            ])
        elif load_time > self.thresholds['load_time_warning']:
            performance_issues.append(f"加载时间较长: {load_time:.2f}秒")
            optimization_suggestions.append("建议使用高性能模式")
        
        # 内存使用检查
        if memory_usage > self.thresholds['memory_critical']:
            performance_issues.append(f"内存使用过高: {memory_usage:.1f}MB")
            optimization_suggestions.extend([
                "启用内存优化模式",
                "使用分页显示",
                "考虑数据筛选"
            ])
        elif memory_usage > self.thresholds['memory_warning']:
            performance_issues.append(f"内存使用较高: {memory_usage:.1f}MB")
            optimization_suggestions.append("建议启用分页显示")
        
        # 记录数量检查
        if record_count > self.thresholds['record_count_critical']:
            performance_issues.append(f"数据量过大: {record_count:,}条记录")
            optimization_suggestions.extend([
                "强烈建议使用筛选功能",
                "启用虚拟滚动",
                "考虑数据分批处理"
            ])
        elif record_count > self.thresholds['record_count_high']:
            performance_issues.append(f"数据量较大: {record_count:,}条记录")
            optimization_suggestions.append("建议使用高性能表格")
        
        # 计算性能指标
        throughput = record_count / max(load_time, 0.001)
        memory_efficiency = record_count / max(memory_usage, 1)
        
        return {
            'load_time': load_time,
            'record_count': record_count,
            'memory_usage': memory_usage,
            'throughput': throughput,
            'memory_efficiency': memory_efficiency,
            'performance_issues': performance_issues,
            'optimization_suggestions': optimization_suggestions,
            'performance_score': self._calculate_performance_score(load_time, memory_usage, record_count)
        }
    
    def _calculate_performance_score(self, load_time: float, memory_usage: float, record_count: int) -> int:
        """计算性能评分（0-100）"""
        score = 100
        
        # 加载时间扣分
        if load_time > 10:
            score -= 40
        elif load_time > 5:
            score -= 20
        elif load_time > 2:
            score -= 10
        
        # 内存使用扣分
        if memory_usage > 500:
            score -= 30
        elif memory_usage > 300:
            score -= 15
        elif memory_usage > 150:
            score -= 5
        
        # 数据量扣分
        if record_count > 20000:
            score -= 20
        elif record_count > 10000:
            score -= 10
        elif record_count > 5000:
            score -= 5
        
        return max(0, score)
    
    def _monitor_performance(self):
        """定期性能监控"""
        try:
            # 获取当前进程信息
            process = psutil.Process()
            memory_info = process.memory_info()
            cpu_percent = process.cpu_percent()
            
            # 检查内存使用
            memory_mb = memory_info.rss / 1024 / 1024
            if memory_mb > self.thresholds['memory_critical']:
                self.performance_warning.emit(
                    "memory_critical", 
                    f"内存使用过高: {memory_mb:.1f}MB，建议重启插件或减少数据量"
                )
            
            # 检查CPU使用
            if cpu_percent > 80:
                self.performance_warning.emit(
                    "cpu_high", 
                    f"CPU使用率过高: {cpu_percent:.1f}%，可能影响系统响应"
                )
                
        except Exception as e:
            print(f"性能监控失败: {str(e)}")
    
    def get_optimization_recommendations(self, current_config: Dict) -> List[str]:
        """获取优化建议
        
        Args:
            current_config: 当前配置信息
            
        Returns:
            List[str]: 优化建议列表
        """
        recommendations = []
        
        # 基于当前配置的建议
        if current_config.get('use_standard_table', False) and current_config.get('record_count', 0) > 1000:
            recommendations.append("建议切换到高性能表格模式以提升显示速度")
        
        if current_config.get('page_size', 100) > 200:
            recommendations.append("建议减少分页大小以提升页面切换速度")
        
        if not current_config.get('use_async_parsing', True):
            recommendations.append("建议启用异步解析以避免界面卡死")
        
        # 基于历史性能的建议
        if self.stats['ui_freeze_count'] > 3:
            recommendations.append("检测到多次界面卡死，建议降低数据处理批次大小")
        
        if self.stats['peak_memory_usage'] > 400:
            recommendations.append("历史内存使用较高，建议启用内存优化模式")
        
        return recommendations
    
    def apply_auto_optimization(self, file_analysis: Dict) -> Dict:
        """应用自动优化
        
        Args:
            file_analysis: 文件分析结果
            
        Returns:
            Dict: 优化配置
        """
        config = {}
        
        # 根据性能等级自动配置
        performance_level = file_analysis.get('performance_level', 'good')
        
        if performance_level == 'critical':
            config.update({
                'use_high_performance_parser': True,
                'use_streaming_mode': True,
                'page_size': 50,
                'progress_interval': 100000,
                'gc_interval': 50000,
                'batch_size': 5000
            })
        elif performance_level == 'warning':
            config.update({
                'use_high_performance_parser': True,
                'use_streaming_mode': False,
                'page_size': 100,
                'progress_interval': 50000,
                'gc_interval': 100000,
                'batch_size': 2000
            })
        elif performance_level == 'moderate':
            config.update({
                'use_high_performance_parser': False,
                'page_size': 200,
                'progress_interval': 20000,
                'batch_size': 1000
            })
        else:
            config.update({
                'use_high_performance_parser': False,
                'page_size': 500,
                'progress_interval': 5000,
                'batch_size': 500
            })
        
        # 记录应用的优化
        self.stats['optimization_applied'].append({
            'timestamp': time.time(),
            'performance_level': performance_level,
            'config': config.copy()
        })
        
        return config
