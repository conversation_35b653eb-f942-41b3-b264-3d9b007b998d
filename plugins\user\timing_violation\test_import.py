#!/usr/bin/env python3
"""
测试时序违例插件导入是否正常
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

def test_import():
    """测试导入"""
    try:
        print("测试导入时序违例插件...")
        
        # 测试主窗口导入
        from plugins.user.timing_violation.main_window import TimingViolationWindow, AsyncDataProcessor
        print("✓ 主窗口导入成功")
        
        # 测试数据模型导入
        from plugins.user.timing_violation.models import ViolationDataModel
        print("✓ 数据模型导入成功")
        
        # 测试解析器导入
        from plugins.user.timing_violation.parser import VioLogParser, AsyncVioLogParser
        print("✓ 解析器导入成功")
        
        # 测试异步数据处理器
        print(f"✓ AsyncDataProcessor 类型: {type(AsyncDataProcessor)}")
        print(f"✓ AsyncDataProcessor 基类: {AsyncDataProcessor.__bases__}")
        
        print("\n🎉 所有导入测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_import()
    sys.exit(0 if success else 1)
