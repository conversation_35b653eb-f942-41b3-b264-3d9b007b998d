"""
时序违例确认插件主窗口

提供时序违例确认的主要用户界面。
"""

import os
import sys
from typing import List, Dict, Optional
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QComboBox, QTableWidget, QTableWidgetItem,
    QFileDialog, QMessageBox, QProgressBar, QGroupBox, QHeaderView,
    QStatusBar, QToolBar, QAction, QCheckBox, QTextEdit, QDialog,
    QDialogButtonBox, QFormLayout, QRadioButton, QButtonGroup, QApplication,
    QAbstractItemView, QScrollBar, QFrame, QScrollArea
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QMetaObject, Q_ARG, QAbstractTableModel, QModelIndex, QVariant, QThread
from PyQt5.QtGui import QFont, QColor, QIcon

# 临时禁用QMessageBox以防止分页时的弹窗问题
class SafeMessageBox:
    """安全的消息框类，避免在分页时出现弹窗"""

    @staticmethod
    def warning(parent, title, message):
        print(f"警告: {title} - {message}")
        return None

    @staticmethod
    def critical(parent, title, message):
        print(f"错误: {title} - {message}")
        return None

    @staticmethod
    def information(parent, title, message):
        print(f"信息: {title} - {message}")
        return None

    @staticmethod
    def question(parent, title, message, buttons=None):
        print(f"询问: {title} - {message}")
        # 默认返回Yes，避免阻塞
        return QMessageBox.Yes if hasattr(QMessageBox, 'Yes') else 16384

# 在分页操作期间使用安全的消息框
_original_messagebox = QMessageBox
_use_safe_messagebox = False

def enable_safe_messagebox():
    """启用安全消息框模式"""
    global _use_safe_messagebox
    _use_safe_messagebox = True

def disable_safe_messagebox():
    """禁用安全消息框模式"""
    global _use_safe_messagebox
    _use_safe_messagebox = False

def get_messagebox():
    """获取当前应该使用的消息框类"""
    return SafeMessageBox if _use_safe_messagebox else _original_messagebox

# 全局替换QMessageBox，确保在分页期间使用安全版本
def safe_messagebox_wrapper(original_method):
    """安全消息框包装器"""
    def wrapper(*args, **kwargs):
        if _use_safe_messagebox:
            # 在安全模式下，将所有消息框调用转为控制台输出
            method_name = original_method.__name__
            if len(args) >= 3:
                parent, title, message = args[0], args[1], args[2]
                print(f"[{method_name}] {title}: {message}")
            return None
        else:
            return original_method(*args, **kwargs)
    return wrapper

# 动态包装QMessageBox的静态方法
_original_warning = QMessageBox.warning
_original_critical = QMessageBox.critical
_original_information = QMessageBox.information
_original_question = QMessageBox.question

QMessageBox.warning = safe_messagebox_wrapper(_original_warning)
QMessageBox.critical = safe_messagebox_wrapper(_original_critical)
QMessageBox.information = safe_messagebox_wrapper(_original_information)
QMessageBox.question = safe_messagebox_wrapper(_original_question)

from plugins.base import NonModalDialog
from .models import ViolationDataModel
from .parser import VioLogParser, AsyncVioLogParser, HighPerformanceVioLogParser, HighPerformanceAsyncParser, CaseInfoParser
from .performance_monitor import PerformanceMonitor, AdaptiveOptimizer
from .performance_config import get_performance_config


class ViolationTableModel(QAbstractTableModel):
    """高性能违例表格数据模型，支持大数据集虚拟滚动"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self._data = []
        self._headers = ["NUM", "层级路径", "时间(ns)", "检查信息", "状态", "确认人", "确认结果", "操作"]
        self._parent_window = parent

    def rowCount(self, parent=QModelIndex()):
        return len(self._data)

    def columnCount(self, parent=QModelIndex()):
        return len(self._headers)

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if orientation == Qt.Horizontal and role == Qt.DisplayRole:
            return self._headers[section]
        return QVariant()

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid() or index.row() >= len(self._data):
            return QVariant()

        violation = self._data[index.row()]
        column = index.column()

        if role == Qt.DisplayRole:
            return self._get_display_data(violation, column)
        elif role == Qt.ForegroundRole:
            return self._get_foreground_color(violation)
        elif role == Qt.BackgroundRole and column == 4:  # 状态列
            return self._get_status_background_color(violation)
        elif role == Qt.TextAlignmentRole:
            if column in [0, 2, 4, 5, 6]:  # NUM, 时间, 状态, 确认人, 确认结果
                return Qt.AlignCenter
            return Qt.AlignLeft | Qt.AlignVCenter
        elif role == Qt.ToolTipRole:
            if column == 1:  # 层级路径
                return violation.get('hier', '')
            elif column == 3:  # 检查信息
                return violation.get('check_info', '')

        return QVariant()

    def _get_display_data(self, violation, column):
        """获取显示数据"""
        if column == 0:  # NUM
            return str(violation.get('num', ''))
        elif column == 1:  # 层级路径
            return violation.get('hier', '')
        elif column == 2:  # 时间(ns)
            time_fs = violation.get('time_fs', 0)
            time_ns = time_fs / 1000000 if time_fs else 0
            return f"{time_ns:.3f}"
        elif column == 3:  # 检查信息
            return violation.get('check_info', '')
        elif column == 4:  # 状态
            status = violation.get('status', 'pending')
            return self._get_status_display(status)
        elif column == 5:  # 确认人
            return violation.get('confirmer', '')
        elif column == 6:  # 确认结果
            result = violation.get('result', '')
            return self._get_result_display(result)
        elif column == 7:  # 操作
            status = violation.get('status', 'pending')
            return "确认" if status == 'pending' else "编辑"

        return ""

    def _get_foreground_color(self, violation):
        """获取前景色"""
        status = violation.get('status', 'pending')
        if status in ['confirmed', 'ignored']:
            return QColor(128, 128, 128)  # 灰色
        return QColor(0, 0, 0)  # 黑色

    def _get_status_background_color(self, violation):
        """获取状态列背景色"""
        status = violation.get('status', 'pending')
        if status == 'confirmed':
            return QColor(144, 238, 144)  # 浅绿色
        elif status == 'ignored':
            return QColor(255, 182, 193)  # 浅红色
        else:
            return QColor(255, 255, 224)  # 浅黄色

    def _get_status_display(self, status):
        """获取状态显示文本"""
        status_map = {
            'pending': '待确认',
            'confirmed': '已确认',
            'ignored': '已忽略'
        }
        return status_map.get(status, status)

    def _get_result_display(self, result):
        """获取结果显示文本"""
        result_map = {
            'pass': '通过',
            'issue': '有问题',
            '': ''
        }
        return result_map.get(result, result)

    def update_data(self, violations):
        """更新数据"""
        self.beginResetModel()
        self._data = violations
        self.endResetModel()

    def get_violation_at_row(self, row):
        """获取指定行的违例数据"""
        if 0 <= row < len(self._data):
            return self._data[row]
        return None


class HighPerformanceTableView(QWidget):
    """高性能表格视图，支持大数据集和虚拟滚动"""

    # 信号定义
    cell_double_clicked = pyqtSignal(int, int)  # 行, 列
    action_button_clicked = pyqtSignal(int, str)  # 行, 动作类型

    def __init__(self, parent=None):
        super().__init__(parent)
        self.model = ViolationTableModel(parent)
        self.init_ui()

        # 分页参数 - 针对大数据集优化
        self.page_size = 50     # 进一步减少每页显示数量，提升加载速度
        self.current_page = 0   # 当前页码（从0开始）
        self.total_pages = 0    # 总页数
        self.row_height = 35    # 行高，与标准模式保持一致

        # 缓存的按钮和性能优化
        self.button_cache = {}
        self.widget_pool = []   # 控件对象池，复用控件减少创建开销
        self.max_pool_size = 500  # 增加控件池最大大小，减少频繁创建

        # 性能优化参数
        self.render_batch_size = 10  # 批量渲染大小
        self.lazy_render_threshold = 20  # 懒加载渲染阈值
        self.memory_cleanup_interval = 50  # 内存清理间隔（页面切换次数）
        self.page_switch_count = 0  # 页面切换计数器

        # 虚拟滚动配置
        self.enable_virtual_scroll = True  # 启用虚拟滚动
        self.virtual_scroll_threshold = 1000  # 虚拟滚动阈值
        self.visible_range = (0, 50)  # 可见范围
        self.viewport_height = 600  # 视口高度

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 创建表头
        self.header_widget = self.create_header()
        layout.addWidget(self.header_widget)

        # 创建分页控件
        self.pagination_widget = self.create_pagination_controls()
        layout.addWidget(self.pagination_widget)

        # 创建滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setFrameStyle(QFrame.Box)
        self.scroll_area.setLineWidth(1)
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        layout.addWidget(self.scroll_area)

        # 创建内容控件
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(0)

        # 将内容控件设置到滚动区域
        self.scroll_area.setWidget(self.content_widget)

        # 初始化行容器
        self.row_widgets = []

    def create_header(self):
        """创建表头"""
        header_widget = QWidget()
        header_widget.setFixedHeight(30)
        header_widget.setStyleSheet("""
            QWidget {
                background-color: #f8f8f8;
                border-bottom: 1px solid #d0d0d0;
                font-weight: bold;
                font-family: "Microsoft YaHei";
            }
        """)

        layout = QHBoxLayout(header_widget)
        layout.setContentsMargins(5, 0, 5, 0)
        layout.setSpacing(0)

        # 列宽配置
        column_widths = [60, 300, 100, 200, 80, 100, 80, 100]
        headers = ["NUM", "层级路径", "时间(ns)", "检查信息", "状态", "确认人", "确认结果", "操作"]

        for i, (header, width) in enumerate(zip(headers, column_widths)):
            label = QLabel(header)
            label.setFixedWidth(width)
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("border-right: 1px solid #d0d0d0; padding: 5px;")
            layout.addWidget(label)

        return header_widget

    def create_pagination_controls(self):
        """创建分页控件"""
        pagination_widget = QWidget()
        pagination_widget.setFixedHeight(40)
        pagination_widget.setStyleSheet("""
            QWidget {
                background-color: #f0f0f0;
                border-bottom: 1px solid #d0d0d0;
            }
            QPushButton {
                background-color: #4a9eff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #3d8ced;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
        """)

        layout = QHBoxLayout(pagination_widget)
        layout.setContentsMargins(10, 5, 10, 5)
        layout.setSpacing(10)

        # 首页按钮
        self.first_page_btn = QPushButton("首页")
        self.first_page_btn.clicked.connect(self.go_to_first_page)
        layout.addWidget(self.first_page_btn)

        # 上一页按钮
        self.prev_page_btn = QPushButton("上一页")
        self.prev_page_btn.clicked.connect(self.go_to_prev_page)
        layout.addWidget(self.prev_page_btn)

        # 页码信息
        self.page_info_label = QLabel("第 1 页，共 1 页")
        layout.addWidget(self.page_info_label)

        # 记录信息
        self.record_info_label = QLabel("显示 0-0 条，共 0 条记录")
        layout.addWidget(self.record_info_label)

        # 弹性空间
        layout.addStretch()

        # 下一页按钮
        self.next_page_btn = QPushButton("下一页")
        self.next_page_btn.clicked.connect(self.go_to_next_page)
        layout.addWidget(self.next_page_btn)

        # 末页按钮
        self.last_page_btn = QPushButton("末页")
        self.last_page_btn.clicked.connect(self.go_to_last_page)
        layout.addWidget(self.last_page_btn)

        return pagination_widget

    def update_data(self, violations):
        """更新数据（安全版本）"""
        try:
            enable_safe_messagebox()  # 防止数据更新过程中的弹窗
            print(f"高性能表格更新数据: {len(violations)} 条记录")

            self.model.update_data(violations)
            print(f"模型数据更新完成，行数: {self.model.rowCount()}")

            self.current_page = 0  # 重置到第一页

            # 检查是否需要启用虚拟滚动
            if self.enable_virtual_scroll and len(violations) > self.virtual_scroll_threshold:
                print(f"启用虚拟滚动模式，数据量: {len(violations)}")
                self.setup_virtual_scroll()
            else:
                self.calculate_pagination()
                print(f"分页计算完成: 总页数 {self.total_pages}, 页面大小 {self.page_size}")

            self.update_pagination_controls()
            self.refresh_current_page()
            print(f"页面刷新完成")
        except Exception as e:
            print(f"更新数据失败: {e}")
            import traceback
            traceback.print_exc()
        finally:
            disable_safe_messagebox()

    def calculate_pagination(self):
        """计算分页信息"""
        total_records = self.model.rowCount()
        self.total_pages = (total_records + self.page_size - 1) // self.page_size if total_records > 0 else 1

        # 确保当前页在有效范围内
        if self.current_page >= self.total_pages:
            self.current_page = max(0, self.total_pages - 1)

    def update_pagination_controls(self):
        """更新分页控件状态"""
        total_records = self.model.rowCount()

        # 更新页码信息
        self.page_info_label.setText(f"第 {self.current_page + 1} 页，共 {self.total_pages} 页")

        # 计算当前页显示的记录范围
        start_record = self.current_page * self.page_size + 1
        end_record = min((self.current_page + 1) * self.page_size, total_records)

        if total_records > 0:
            self.record_info_label.setText(f"显示 {start_record}-{end_record} 条，共 {total_records} 条记录")
        else:
            self.record_info_label.setText("显示 0-0 条，共 0 条记录")

        # 更新按钮状态
        self.first_page_btn.setEnabled(self.current_page > 0)
        self.prev_page_btn.setEnabled(self.current_page > 0)
        self.next_page_btn.setEnabled(self.current_page < self.total_pages - 1)
        self.last_page_btn.setEnabled(self.current_page < self.total_pages - 1)

    def go_to_first_page(self):
        """跳转到首页（彻底安全版本）"""
        try:
            enable_safe_messagebox()  # 防止分页过程中的弹窗
            if self.current_page != 0:
                self.current_page = 0
                self.update_pagination_controls()
                self.refresh_current_page()
        except Exception as e:
            print(f"跳转到首页失败: {e}")
        finally:
            disable_safe_messagebox()

    def go_to_prev_page(self):
        """跳转到上一页（彻底安全版本）"""
        try:
            enable_safe_messagebox()  # 防止分页过程中的弹窗
            if self.current_page > 0:
                self.current_page -= 1
                self.update_pagination_controls()
                self.refresh_current_page()
        except Exception as e:
            print(f"跳转到上一页失败: {e}")
        finally:
            disable_safe_messagebox()

    def go_to_next_page(self):
        """跳转到下一页（彻底安全版本）"""
        try:
            enable_safe_messagebox()  # 防止分页过程中的弹窗
            if self.current_page < self.total_pages - 1:
                self.current_page += 1
                self.update_pagination_controls()
                self.refresh_current_page()
        except Exception as e:
            print(f"跳转到下一页失败: {e}")
        finally:
            disable_safe_messagebox()

    def go_to_last_page(self):
        """跳转到末页（彻底安全版本）"""
        try:
            enable_safe_messagebox()  # 防止分页过程中的弹窗
            if self.current_page != self.total_pages - 1:
                self.current_page = max(0, self.total_pages - 1)
                self.update_pagination_controls()
                self.refresh_current_page()
        except Exception as e:
            print(f"跳转到末页失败: {e}")
        finally:
            disable_safe_messagebox()

    def refresh_current_page(self):
        """刷新当前页显示（高性能优化版本）"""
        try:
            # 启用安全消息框模式，防止任何弹窗
            enable_safe_messagebox()

            import time
            start_time = time.time()

            # 智能内存清理：定期清理对象池
            self.page_switch_count += 1
            if self.page_switch_count % self.memory_cleanup_interval == 0:
                self._cleanup_widget_pool()

            # 批量处理：先收集所有要移除的控件，然后一次性处理
            widgets_to_remove = []
            while self.content_layout.count():
                child = self.content_layout.takeAt(0)
                if child.widget():
                    widgets_to_remove.append(child.widget())

            # 智能控件回收：优先回收相同类型的控件
            self._recycle_widgets_intelligently(widgets_to_remove)

            # 清除行控件列表
            self.row_widgets.clear()

            # 计算当前页的数据范围
            start_index = self.current_page * self.page_size
            end_index = min(start_index + self.page_size, self.model.rowCount())

            print(f"刷新页面 {self.current_page + 1}/{self.total_pages}: 索引范围 {start_index}-{end_index}, 总数据量 {self.model.rowCount()}")

            # 分批渲染：避免一次性创建过多控件
            new_widgets = []
            total_rows = end_index - start_index

            if total_rows == 0:
                print("警告: 当前页没有数据要显示")
                return

            for batch_start in range(start_index, end_index, self.render_batch_size):
                batch_end = min(batch_start + self.render_batch_size, end_index)
                batch_widgets = []

                for row in range(batch_start, batch_end):
                    try:
                        row_widget = self.create_row_widget_optimized(row)
                        if row_widget:
                            batch_widgets.append(row_widget)
                            self.row_widgets.append(row_widget)
                    except Exception as e:
                        # 静默处理单行创建错误，避免弹窗
                        print(f"创建行控件失败 (行 {row}): {e}")

                new_widgets.extend(batch_widgets)

                # 分批添加到布局，减少UI阻塞
                for widget in batch_widgets:
                    try:
                        self.content_layout.addWidget(widget)
                    except Exception as e:
                        print(f"添加控件到布局失败: {e}")

                # 每批次后短暂让出控制权，保持UI响应
                if batch_end < end_index:
                    QApplication.processEvents()

            # 添加弹性空间
            self.content_layout.addStretch()

            # 延迟更新几何信息，避免频繁重绘
            QTimer.singleShot(10, self._delayed_geometry_update)

            # 性能统计和优化建议
            load_time = time.time() - start_time
            self._update_performance_stats(load_time, len(new_widgets))

        except Exception as e:
            # 静默处理整个刷新过程的错误
            print(f"刷新当前页失败: {e}")
            # 在状态栏显示错误而不是弹窗
            if hasattr(self, 'status_label'):
                self.status_label.setText(f"页面刷新失败: {str(e)[:30]}")
                QTimer.singleShot(3000, lambda: self.status_label.setText("就绪"))
        finally:
            # 确保在完成后恢复正常的消息框模式
            disable_safe_messagebox()

    def _cleanup_widget_pool(self):
        """智能清理控件对象池"""
        try:
            # 保留一定数量的控件，清理多余的
            target_size = self.max_pool_size // 2
            if len(self.widget_pool) > target_size:
                widgets_to_remove = self.widget_pool[target_size:]
                self.widget_pool = self.widget_pool[:target_size]

                # 延迟删除控件，避免阻塞UI
                for widget in widgets_to_remove:
                    widget.deleteLater()

                print(f"清理控件池: 移除 {len(widgets_to_remove)} 个控件，保留 {len(self.widget_pool)} 个")
        except Exception as e:
            print(f"清理控件池失败: {e}")

    def _recycle_widgets_intelligently(self, widgets_to_remove):
        """智能控件回收策略"""
        try:
            for widget in widgets_to_remove:
                try:
                    # 检查控件状态和类型
                    if (len(self.widget_pool) < self.max_pool_size and
                        widget.isVisible() and
                        not widget.isHidden()):

                        # 重置控件状态
                        widget.setVisible(False)
                        widget.setParent(None)

                        # 完全清理控件内容和布局
                        self._completely_clear_widget(widget)

                        self.widget_pool.append(widget)
                    else:
                        widget.deleteLater()
                except Exception as e:
                    print(f"回收单个控件失败: {e}")
                    widget.deleteLater()
        except Exception as e:
            print(f"智能控件回收失败: {e}")

    def _clear_widget_layout(self, layout):
        """清理控件布局内容"""
        try:
            while layout.count():
                child = layout.takeAt(0)
                if child.widget():
                    child.widget().setParent(None)
        except Exception as e:
            print(f"清理布局失败: {e}")

    def _completely_clear_widget(self, widget):
        """完全清理控件，包括布局和内容"""
        try:
            # 清理布局内容
            if widget.layout():
                layout = widget.layout()
                # 清理所有子控件
                while layout.count():
                    child = layout.takeAt(0)
                    if child.widget():
                        child.widget().setParent(None)
                    elif child.layout():
                        # 递归清理子布局
                        self._clear_layout_recursive(child.layout())

                # 删除布局本身
                layout.setParent(None)
                layout.deleteLater()

            # 重置控件属性
            widget.setStyleSheet("")
            widget.setToolTip("")

        except Exception as e:
            print(f"完全清理控件失败: {e}")

    def _completely_reset_widget(self, widget):
        """完全重置控件状态，避免布局冲突"""
        try:
            # 完全移除现有布局
            if widget.layout():
                old_layout = widget.layout()
                widget.setLayout(None)  # 先移除布局引用
                old_layout.deleteLater()  # 延迟删除

            # 清理所有子控件
            for child in widget.findChildren(QWidget):
                child.setParent(None)
                child.deleteLater()

            # 重置控件属性
            widget.setStyleSheet("")
            widget.setToolTip("")

        except Exception as e:
            print(f"重置控件失败: {e}")
            # 创建新控件作为备选方案
            return QWidget()

        return widget

    def _clear_layout_recursive(self, layout):
        """递归清理布局"""
        try:
            while layout.count():
                child = layout.takeAt(0)
                if child.widget():
                    child.widget().setParent(None)
                elif child.layout():
                    self._clear_layout_recursive(child.layout())
        except Exception as e:
            print(f"递归清理布局失败: {e}")

    def _delayed_geometry_update(self):
        """延迟几何更新"""
        try:
            self.content_widget.adjustSize()
        except Exception as e:
            print(f"延迟几何更新失败: {e}")

    def _update_performance_stats(self, load_time, widget_count):
        """更新性能统计"""
        try:
            if load_time > 0.05:  # 超过50ms记录性能信息
                print(f"页面刷新耗时: {load_time:.3f}秒 (控件数: {widget_count}, 对象池: {len(self.widget_pool)})")

            # 性能建议
            if load_time > 0.2:
                print("性能建议: 考虑进一步减少页面大小或启用懒加载")
            elif widget_count > 30:
                print("性能建议: 当前页面控件较多，建议减少页面大小")
        except Exception as e:
            print(f"更新性能统计失败: {e}")

    def setup_virtual_scroll(self):
        """设置虚拟滚动"""
        try:
            total_records = self.model.rowCount()
            # 计算虚拟滚动参数
            self.total_pages = (total_records + self.page_size - 1) // self.page_size if total_records > 0 else 1

            # 设置滚动区域的总高度
            total_height = total_records * self.row_height
            self.content_widget.setMinimumHeight(total_height)

            # 连接滚动事件
            if hasattr(self, 'scroll_area'):
                self.scroll_area.verticalScrollBar().valueChanged.connect(self.on_scroll_changed)

            print(f"虚拟滚动设置完成: 总记录数 {total_records}, 总高度 {total_height}px")

        except Exception as e:
            print(f"设置虚拟滚动失败: {e}")

    def on_scroll_changed(self, scroll_position):
        """滚动位置改变事件"""
        try:
            # 计算可见范围
            start_row = max(0, scroll_position // self.row_height - 5)  # 预加载5行
            end_row = min(self.model.rowCount(),
                         start_row + self.viewport_height // self.row_height + 10)  # 预加载10行

            new_visible_range = (start_row, end_row)

            # 只有当可见范围发生显著变化时才更新
            if abs(new_visible_range[0] - self.visible_range[0]) > 5 or \
               abs(new_visible_range[1] - self.visible_range[1]) > 5:
                self.visible_range = new_visible_range
                self.refresh_visible_rows()

        except Exception as e:
            print(f"处理滚动事件失败: {e}")

    def refresh_visible_rows(self):
        """只刷新可见行（虚拟滚动核心方法）"""
        try:
            enable_safe_messagebox()

            # 清除现有控件
            widgets_to_remove = []
            while self.content_layout.count():
                child = self.content_layout.takeAt(0)
                if child.widget():
                    widgets_to_remove.append(child.widget())

            self._recycle_widgets_intelligently(widgets_to_remove)
            self.row_widgets.clear()

            start_row, end_row = self.visible_range
            print(f"虚拟滚动刷新可见行: {start_row}-{end_row}")

            # 只渲染可见范围内的行
            for row in range(start_row, end_row):
                try:
                    row_widget = self.create_row_widget_optimized(row)
                    if row_widget:
                        # 设置控件的绝对位置
                        row_widget.move(0, row * self.row_height)
                        self.content_layout.addWidget(row_widget)
                        self.row_widgets.append(row_widget)
                except Exception as e:
                    print(f"创建可见行控件失败 (行 {row}): {e}")

            # 添加弹性空间
            self.content_layout.addStretch()

        except Exception as e:
            print(f"刷新可见行失败: {e}")
        finally:
            disable_safe_messagebox()

    def create_row_widget_optimized(self, row):
        """创建行控件（优化版本）"""
        # 尝试从对象池复用控件
        if self.widget_pool:
            row_widget = self.widget_pool.pop()
            row_widget.setVisible(True)
            # 完全重置控件状态，避免布局冲突
            row_widget = self._completely_reset_widget(row_widget)
        else:
            row_widget = QWidget()

        return self.setup_row_widget(row_widget, row)

    def setup_row_widget(self, row_widget, row):
        """设置行控件内容（分离创建和设置逻辑，优化版本避免错误弹窗）"""
        try:
            # 注意：这里的 row 是绝对索引，直接使用
            violation = self.model.get_violation_at_row(row)
            if not violation:
                print(f"警告: 无法获取行 {row} 的违例数据，总数据量: {self.model.rowCount()}")
                return None

            row_widget.setFixedHeight(self.row_height)

            # 计算相对于当前页的行索引（用于UI事件）
            relative_row = row - self.current_page * self.page_size

            # 设置行样式（基于绝对行索引）
            if row % 2 == 0:
                row_widget.setStyleSheet("background-color: white;")
            else:
                row_widget.setStyleSheet("background-color: #f9f9f9;")

            # 安全的布局管理 - 避免重复添加布局
            existing_layout = row_widget.layout()
            if existing_layout:
                # 清除现有布局中的所有控件
                while existing_layout.count():
                    child = existing_layout.takeAt(0)
                    if child.widget():
                        child.widget().setParent(None)
                layout = existing_layout
                # 重新设置布局属性
                layout.setContentsMargins(5, 0, 5, 0)
                layout.setSpacing(0)
            else:
                # 只有在确实没有布局时才创建新布局
                layout = QHBoxLayout()
                layout.setContentsMargins(5, 0, 5, 0)
                layout.setSpacing(0)
                row_widget.setLayout(layout)

            # 列宽配置
            column_widths = [60, 300, 100, 200, 80, 100, 80, 100]

            # 安全地创建列控件
            for col in range(min(self.model.columnCount(), len(column_widths))):
                try:
                    if col == 7:  # 操作列
                        button = self.create_action_button(relative_row, violation)
                        if button:
                            button.setFixedWidth(column_widths[col])
                            layout.addWidget(button)
                    else:
                        label = self.create_cell_label(relative_row, col, violation, column_widths[col])
                        if label:
                            layout.addWidget(label)
                except Exception as e:
                    # 静默处理单个列的错误，避免弹窗
                    print(f"创建列 {col} 控件失败: {e}")
                    # 添加占位标签
                    placeholder = QLabel("--")
                    placeholder.setFixedWidth(column_widths[col])
                    layout.addWidget(placeholder)

            return row_widget

        except Exception as e:
            # 静默处理整个行控件创建错误
            print(f"设置行控件失败 (行 {row}): {e}")
            return None



    def create_row_widget(self, row):
        """创建行控件"""
        violation = self.model.get_violation_at_row(row)
        if not violation:
            return None

        row_widget = QWidget()
        row_widget.setFixedHeight(self.row_height)

        # 计算相对于当前页的行索引（用于UI事件）
        relative_row = row - self.current_page * self.page_size

        # 设置行样式（基于绝对行索引）
        if row % 2 == 0:
            row_widget.setStyleSheet("background-color: white;")
        else:
            row_widget.setStyleSheet("background-color: #f9f9f9;")

        layout = QHBoxLayout(row_widget)
        layout.setContentsMargins(5, 0, 5, 0)
        layout.setSpacing(0)

        # 列宽配置
        column_widths = [60, 300, 100, 200, 80, 100, 80, 100]

        for col in range(self.model.columnCount()):
            if col == 7:  # 操作列
                button = self.create_action_button(relative_row, violation)
                button.setFixedWidth(column_widths[col])
                layout.addWidget(button)
            else:
                label = self.create_cell_label(relative_row, col, violation, column_widths[col])
                layout.addWidget(label)

        return row_widget

    def create_cell_label(self, row, col, violation, width):
        """创建单元格标签（优化版本，避免错误弹窗）"""
        try:
            index = self.model.index(row, col)
            text = self.model.data(index, Qt.DisplayRole)

            label = QLabel(str(text) if text is not None else "")
            label.setFixedWidth(width)
            label.setStyleSheet("border-right: 1px solid #e0e0e0; padding: 5px;")

            # 设置对齐方式
            if col in [0, 2, 4, 5, 6]:  # NUM, 时间, 状态, 确认人, 确认结果
                label.setAlignment(Qt.AlignCenter)
            else:
                label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)

            # 安全地设置颜色
            try:
                fg_color = self.model.data(index, Qt.ForegroundRole)
                if fg_color and hasattr(fg_color, 'name'):
                    label.setStyleSheet(label.styleSheet() + f"color: {fg_color.name()};")
            except Exception as e:
                print(f"设置前景色失败: {e}")

            # 安全地设置状态列背景色
            if col == 4:
                try:
                    bg_color = self.model.data(index, Qt.BackgroundRole)
                    if bg_color and hasattr(bg_color, 'name'):
                        label.setStyleSheet(label.styleSheet() + f"background-color: {bg_color.name()};")
                except Exception as e:
                    print(f"设置背景色失败: {e}")

            # 层级路径列支持双击复制
            if col == 1:
                def make_mouse_event_handler(r, c):
                    def mouse_event_handler(event):
                        try:
                            if event.type() == event.MouseButtonDblClick:
                                self.cell_double_clicked.emit(r, c)
                        except Exception as e:
                            print(f"双击事件处理失败: {e}")
                    return mouse_event_handler
                label.mouseDoubleClickEvent = make_mouse_event_handler(row, col)

            # 安全地设置工具提示
            try:
                tooltip = self.model.data(index, Qt.ToolTipRole)
                if tooltip:
                    label.setToolTip(str(tooltip))
            except Exception as e:
                print(f"设置工具提示失败: {e}")

            return label

        except Exception as e:
            # 如果创建失败，返回一个简单的占位标签
            print(f"创建单元格标签失败 (行 {row}, 列 {col}): {e}")
            placeholder = QLabel("--")
            placeholder.setFixedWidth(width)
            placeholder.setStyleSheet("border-right: 1px solid #e0e0e0; padding: 5px;")
            return placeholder

    def create_action_button(self, row, violation):
        """创建操作按钮（彻底重写，避免信号问题导致的弹窗）"""
        try:
            # 启用安全消息框模式，防止按钮创建过程中的弹窗
            enable_safe_messagebox()

            status = violation.get('status', 'pending')
            violation_id = violation.get('id')

            # 验证数据有效性
            if not violation_id:
                print(f"警告: 违例记录缺少ID，行: {row}")
                placeholder_button = QPushButton("--")
                placeholder_button.setEnabled(False)
                return placeholder_button

            if status == 'pending':
                button = QPushButton("确认")
                button.setStyleSheet("""
                    QPushButton {
                        background-color: #4a9eff;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 4px 8px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #3d8ced;
                    }
                """)
            else:
                button = QPushButton("编辑")
                button.setStyleSheet("""
                    QPushButton {
                        color: #808080;
                        background-color: #f0f0f0;
                        border: 1px solid #c0c0c0;
                        border-radius: 4px;
                        padding: 4px 8px;
                    }
                    QPushButton:hover {
                        background-color: #e0e0e0;
                    }
                """)

            # 完全重写信号连接，避免lambda闭包问题
            # 将数据存储在按钮的属性中，而不是使用闭包
            button.setProperty('violation_id', violation_id)
            button.setProperty('action_type', status)
            button.setProperty('row_index', row)

            # 使用简单的槽函数，避免复杂的信号处理
            button.clicked.connect(self.handle_action_button_click)

            return button

        except Exception as e:
            # 如果按钮创建失败，返回一个简单的占位按钮
            print(f"创建操作按钮失败: {e}")
            placeholder_button = QPushButton("--")
            placeholder_button.setEnabled(False)
            return placeholder_button
        finally:
            # 确保恢复正常的消息框模式
            disable_safe_messagebox()

    def handle_action_button_click(self):
        """处理操作按钮点击（新的安全处理方法）"""
        try:
            # 获取发送信号的按钮
            button = self.sender()
            if not button:
                return

            # 从按钮属性中获取数据，避免闭包问题
            violation_id = button.property('violation_id')
            action_type = button.property('action_type')
            row_index = button.property('row_index')

            # 验证数据有效性
            if not violation_id:
                print(f"按钮缺少violation_id属性")
                return

            # 根据操作类型执行相应操作
            if action_type == 'pending':
                self.confirm_single_violation(violation_id)
            else:
                self.edit_confirmation(violation_id)

        except Exception as e:
            # 静默处理错误，绝对不显示弹窗
            print(f"处理按钮点击失败: {e}")
            # 可选：在状态栏显示错误
            if hasattr(self, 'status_label'):
                self.status_label.setText(f"操作失败")
                QTimer.singleShot(2000, lambda: self.status_label.setText("就绪"))

    def handle_standard_table_button_click(self):
        """处理标准表格中的按钮点击（安全版本）"""
        try:
            # 获取发送信号的按钮
            button = self.sender()
            if not button:
                return

            # 从按钮属性中获取数据
            violation_id = button.property('violation_id')
            action_type = button.property('action_type')

            # 验证数据有效性
            if not violation_id:
                print(f"标准表格按钮缺少violation_id属性")
                return

            # 根据操作类型执行相应操作
            if action_type == 'pending':
                self.confirm_single_violation(violation_id)
            else:
                self.edit_confirmation(violation_id)

        except Exception as e:
            # 静默处理错误，绝对不显示弹窗
            print(f"处理标准表格按钮点击失败: {e}")
            if hasattr(self, 'status_label'):
                self.status_label.setText(f"操作失败")
                QTimer.singleShot(2000, lambda: self.status_label.setText("就绪"))


class TimingViolationWindow(NonModalDialog):
    """时序违例确认主窗口"""
    
    def __init__(self, parent=None):
        super().__init__(parent, "后仿时序违例确认工具")
        self.setWindowTitle("后仿时序违例确认工具")
        self.resize(1400, 900)
        self.setMinimumSize(1200, 700)
        
        # 数据模型
        self.data_model = ViolationDataModel()
        self.parser = VioLogParser()
        self.async_parser = None

        # 当前数据
        self.current_violations = []
        self.current_case_name = ""
        self.current_corner = ""
        self.current_file_path = ""

        # 性能优化标志
        self.use_high_performance_table = False
        self.performance_threshold = 500  # 降低阈值，超过500行就使用高性能表格，避免GUI卡死

        # 性能配置管理
        self.perf_config_manager = get_performance_config()
        self.perf_config = self.perf_config_manager.get_config()

        # 应用性能配置到组件
        self._apply_performance_config()

        # 性能监控系统
        self.performance_monitor = PerformanceMonitor()
        self.adaptive_optimizer = AdaptiveOptimizer(self.performance_monitor)

        # 传统性能统计（保持兼容性）
        self.performance_stats = {
            'last_load_time': 0,
            'last_record_count': 0,
            'memory_usage_mb': 0
        }

        # 连接性能监控信号
        self.performance_monitor.performance_warning.connect(self.on_performance_warning)
        self.performance_monitor.optimization_suggested.connect(self.on_optimization_suggested)
        self.performance_monitor.memory_threshold_exceeded.connect(self.on_memory_threshold_exceeded)
        
        # 初始化UI
        self.init_ui()
        self.apply_runsim_theme()
        
        # 连接信号
        self.connect_signals()

    def _apply_performance_config(self):
        """应用性能配置到各个组件"""
        try:
            # 更新性能阈值
            self.performance_threshold = self.perf_config.performance_threshold

            # 更新数据模型缓存配置
            if hasattr(self.data_model, 'cache_max_size'):
                self.data_model.cache_max_size = self.perf_config.cache_max_size
                self.data_model.cache_ttl = self.perf_config.cache_ttl

            print(f"性能配置已应用: 阈值={self.performance_threshold}, "
                  f"缓存大小={self.perf_config.cache_max_size}")

        except Exception as e:
            print(f"应用性能配置失败: {e}")

    def _update_high_performance_table_config(self):
        """更新高性能表格配置"""
        try:
            if hasattr(self, 'high_performance_table'):
                table = self.high_performance_table

                # 应用配置
                table.page_size = self.perf_config.page_size
                table.render_batch_size = self.perf_config.render_batch_size
                table.lazy_render_threshold = self.perf_config.lazy_render_threshold
                table.max_pool_size = self.perf_config.max_pool_size
                table.memory_cleanup_interval = self.perf_config.memory_cleanup_interval

                print(f"高性能表格配置已更新: 页面大小={table.page_size}, "
                      f"对象池大小={table.max_pool_size}")

        except Exception as e:
            print(f"更新高性能表格配置失败: {e}")

    def show_performance_settings(self):
        """显示性能设置对话框"""
        try:
            from .performance_settings_dialog import PerformanceSettingsDialog

            dialog = PerformanceSettingsDialog(self, self.perf_config_manager)
            if dialog.exec_() == dialog.Accepted:
                # 重新加载配置
                self.perf_config = self.perf_config_manager.get_config()
                self._apply_performance_config()
                self._update_high_performance_table_config()

                # 显示配置摘要
                summary = self.perf_config_manager.get_config_summary()
                self.status_label.setText(f"性能配置已更新: {summary['当前预设']}")
                QTimer.singleShot(3000, lambda: self.status_label.setText("就绪"))

        except ImportError:
            # 如果对话框不存在，显示简单的配置信息
            summary = self.perf_config_manager.get_config_summary()
            config_text = "\n".join([f"{k}: {v}" for k, v in summary.items()])

            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(self, "性能配置", f"当前性能配置:\n\n{config_text}")
        except Exception as e:
            print(f"显示性能设置失败: {e}")

    def on_performance_warning(self, warning_type: str, details: dict):
        """处理性能警告"""
        try:
            if warning_type == 'ui_slow':
                message = f"UI响应较慢 ({details['duration']:.3f}s)"
                self.perf_label.setText(f"⚠️ {message}")
                QTimer.singleShot(5000, lambda: self.perf_label.setText(""))

            elif warning_type == 'memory_warning':
                message = f"内存使用较高 ({details['memory_mb']:.1f}MB)"
                self.perf_label.setText(f"⚠️ {message}")

            elif warning_type == 'memory_critical':
                message = f"内存使用过高 ({details['memory_mb']:.1f}MB)"
                self.perf_label.setText(f"🚨 {message}")
                # 触发内存清理
                self._trigger_memory_cleanup()

        except Exception as e:
            print(f"处理性能警告失败: {e}")

    def on_optimization_suggested(self, optimization_type: str, suggestions: list):
        """处理优化建议"""
        try:
            if optimization_type == 'performance' and suggestions:
                # 显示优化建议
                suggestion_text = "; ".join(suggestions[:2])  # 只显示前两个建议
                self.perf_label.setText(f"💡 {suggestion_text}")
                QTimer.singleShot(10000, lambda: self.perf_label.setText(""))

                # 自动应用某些优化
                self._apply_auto_optimizations(suggestions)

        except Exception as e:
            print(f"处理优化建议失败: {e}")

    def on_memory_threshold_exceeded(self, memory_mb: float):
        """处理内存阈值超标"""
        try:
            print(f"内存使用超标: {memory_mb:.1f}MB，触发紧急清理")
            self._trigger_emergency_cleanup()
        except Exception as e:
            print(f"处理内存超标失败: {e}")

    def _trigger_memory_cleanup(self):
        """触发内存清理"""
        try:
            # 清理高性能表格的对象池
            if hasattr(self, 'high_performance_table'):
                self.high_performance_table._cleanup_widget_pool()

            # 清理数据模型缓存
            if hasattr(self.data_model, 'clear_cache'):
                self.data_model.clear_cache()

            # 强制垃圾回收
            import gc
            gc.collect()

            print("执行内存清理")
        except Exception as e:
            print(f"内存清理失败: {e}")

    def _trigger_emergency_cleanup(self):
        """触发紧急清理"""
        try:
            # 执行常规清理
            self._trigger_memory_cleanup()

            # 减少页面大小
            if hasattr(self, 'high_performance_table'):
                current_size = self.high_performance_table.page_size
                new_size = max(20, current_size // 2)
                self.high_performance_table.page_size = new_size
                print(f"紧急减少页面大小: {current_size} -> {new_size}")

            # 清空部分缓存
            if hasattr(self.data_model, 'query_cache'):
                cache_size = len(self.data_model.query_cache)
                self.data_model.query_cache.clear()
                print(f"清空查询缓存: {cache_size} 条记录")

        except Exception as e:
            print(f"紧急清理失败: {e}")

    def _apply_auto_optimizations(self, suggestions: list):
        """自动应用优化建议"""
        try:
            for suggestion in suggestions:
                if "减少页面大小" in suggestion and hasattr(self, 'high_performance_table'):
                    current_size = self.high_performance_table.page_size
                    new_size = max(20, current_size - 10)
                    if new_size != current_size:
                        self.high_performance_table.page_size = new_size
                        print(f"自动优化: 页面大小 {current_size} -> {new_size}")

                elif "启用内存优化" in suggestion:
                    self._trigger_memory_cleanup()

                elif "增加缓存大小" in suggestion and hasattr(self.data_model, 'cache_max_size'):
                    current_size = self.data_model.cache_max_size
                    new_size = min(200, current_size + 20)
                    if new_size != current_size:
                        self.data_model.cache_max_size = new_size
                        print(f"自动优化: 缓存大小 {current_size} -> {new_size}")

        except Exception as e:
            print(f"自动优化失败: {e}")

    def init_ui(self):
        """初始化用户界面"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(5)
        main_layout.setContentsMargins(5, 5, 5, 5)

        # 创建工具栏
        toolbar = self.create_toolbar()
        main_layout.addWidget(toolbar)

        # 创建控制面板
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel)

        # 创建违例列表
        violation_table = self.create_violation_table()
        main_layout.addWidget(violation_table)

        # 创建状态栏
        status_bar = self.create_status_bar()
        main_layout.addWidget(status_bar)

        # 设置布局比例
        main_layout.setStretchFactor(toolbar, 0)
        main_layout.setStretchFactor(control_panel, 0)
        main_layout.setStretchFactor(violation_table, 1)
        main_layout.setStretchFactor(status_bar, 0)
    
    def create_toolbar(self):
        """创建工具栏"""
        # 创建工具栏容器
        toolbar_widget = QWidget()
        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(5, 5, 5, 5)
        
        # 选择文件按钮
        self.select_file_btn_toolbar = QPushButton("选择文件")
        self.select_file_btn_toolbar.setToolTip("选择vio_summary.log文件")
        self.select_file_btn_toolbar.clicked.connect(self.select_log_file)
        toolbar_layout.addWidget(self.select_file_btn_toolbar)

        # 分隔符
        toolbar_layout.addWidget(QLabel("|"))

        # 刷新按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.setToolTip("刷新违例列表")
        self.refresh_btn.clicked.connect(self.refresh_data)
        toolbar_layout.addWidget(self.refresh_btn)

        # 分隔符
        toolbar_layout.addWidget(QLabel("|"))

        # 导出按钮
        self.export_excel_btn = QPushButton("导出Excel")
        self.export_excel_btn.setToolTip("导出为Excel文件")
        self.export_excel_btn.clicked.connect(self.export_to_excel)
        toolbar_layout.addWidget(self.export_excel_btn)

        self.export_csv_btn = QPushButton("导出CSV")
        self.export_csv_btn.setToolTip("导出为CSV文件")
        self.export_csv_btn.clicked.connect(self.export_to_csv)
        toolbar_layout.addWidget(self.export_csv_btn)

        # 分隔符
        toolbar_layout.addWidget(QLabel("|"))

        # 清除历史按钮
        self.clear_btn = QPushButton("清除历史")
        self.clear_btn.setToolTip("清除当前用例的历史数据")
        self.clear_btn.clicked.connect(self.clear_history)
        toolbar_layout.addWidget(self.clear_btn)

        # 历史管理按钮
        self.history_mgmt_btn = QPushButton("历史管理")
        self.history_mgmt_btn.setToolTip("管理历史确认模式")
        self.history_mgmt_btn.clicked.connect(self.show_history_management)
        toolbar_layout.addWidget(self.history_mgmt_btn)

        # 添加弹性空间
        toolbar_layout.addStretch()

        return toolbar_widget
    
    def create_control_panel(self) -> QGroupBox:
        """创建控制面板"""
        group_box = QGroupBox("控制面板")
        layout = QVBoxLayout(group_box)
        
        # 第一行：文件和基本信息
        first_row = QHBoxLayout()
        
        # 文件路径
        first_row.addWidget(QLabel("文件路径:"))
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setReadOnly(True)
        self.file_path_edit.setPlaceholderText("请选择vio_summary.log文件")
        first_row.addWidget(self.file_path_edit)
        
        self.select_file_btn = QPushButton("选择文件...")
        self.select_file_btn.clicked.connect(self.select_log_file)
        first_row.addWidget(self.select_file_btn)
        
        layout.addLayout(first_row)
        
        # 第二行：用例信息和复位时间
        second_row = QHBoxLayout()
        
        # 用例名称
        second_row.addWidget(QLabel("用例名称:"))
        self.case_name_edit = QLineEdit()
        self.case_name_edit.setPlaceholderText("自动检测")
        second_row.addWidget(self.case_name_edit)
        
        # Corner选择
        second_row.addWidget(QLabel("Corner:"))
        self.corner_combo = QComboBox()
        self.corner_combo.addItem("请选择...")
        self.corner_combo.addItems(CaseInfoParser.get_valid_corners())
        second_row.addWidget(self.corner_combo)
        
        # 复位时间
        second_row.addWidget(QLabel("复位时间(ns):"))
        self.reset_time_edit = QLineEdit()
        self.reset_time_edit.setPlaceholderText("1000")
        self.reset_time_edit.setText("1000")
        second_row.addWidget(self.reset_time_edit)
        
        layout.addLayout(second_row)
        
        # 第三行：进度和操作按钮
        third_row = QHBoxLayout()
        
        # 进度信息
        self.progress_label = QLabel("进度: 已确认 0/0 (0%)")
        third_row.addWidget(self.progress_label)
        
        third_row.addStretch()
        
        # 操作按钮
        self.auto_confirm_btn = QPushButton("自动确认")
        self.auto_confirm_btn.setToolTip("根据复位时间自动确认违例")
        self.auto_confirm_btn.clicked.connect(self.auto_confirm_violations)
        third_row.addWidget(self.auto_confirm_btn)
        
        self.batch_confirm_btn = QPushButton("批量确认")
        self.batch_confirm_btn.setToolTip("批量确认选中的违例")
        self.batch_confirm_btn.clicked.connect(self.batch_confirm_violations)
        third_row.addWidget(self.batch_confirm_btn)
        
        self.confirm_all_btn = QPushButton("全部确认")
        self.confirm_all_btn.setToolTip("确认所有待确认的违例")
        self.confirm_all_btn.clicked.connect(self.confirm_all_violations)
        third_row.addWidget(self.confirm_all_btn)

        self.apply_history_btn = QPushButton("应用历史")
        self.apply_history_btn.setToolTip("应用历史确认记录到匹配的违例")
        self.apply_history_btn.clicked.connect(self.apply_historical_confirmations)
        third_row.addWidget(self.apply_history_btn)
        
        layout.addLayout(third_row)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        return group_box
    
    def create_violation_table(self) -> QGroupBox:
        """创建违例列表表格"""
        group_box = QGroupBox("时序违例列表")
        layout = QVBoxLayout(group_box)

        # 创建性能提示标签
        self.performance_info_label = QLabel("")
        self.performance_info_label.setStyleSheet("color: #666; font-size: 12px; padding: 2px;")
        self.performance_info_label.setVisible(False)
        layout.addWidget(self.performance_info_label)

        # 创建标准表格（用于小数据集）
        self.violation_table = QTableWidget()
        self.violation_table.setColumnCount(8)

        # 设置表头
        headers = ["NUM", "层级路径", "时间(ns)", "检查信息", "状态", "确认人", "确认结果", "操作"]
        self.violation_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.violation_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.violation_table.setAlternatingRowColors(True)
        self.violation_table.setSortingEnabled(True)

        # 设置行高
        self.violation_table.verticalHeader().setDefaultSectionSize(35)
        self.violation_table.verticalHeader().setMinimumSectionSize(35)

        # 设置列宽
        header = self.violation_table.horizontalHeader()
        header.setStretchLastSection(False)
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # NUM
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 层级路径
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # 时间
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # 检查信息
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # 状态
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # 确认人
        header.setSectionResizeMode(6, QHeaderView.Fixed)  # 确认结果
        header.setSectionResizeMode(7, QHeaderView.Fixed)  # 操作

        # 设置固定列宽
        self.violation_table.setColumnWidth(0, 60)   # NUM
        self.violation_table.setColumnWidth(2, 100)  # 时间
        self.violation_table.setColumnWidth(4, 80)   # 状态
        self.violation_table.setColumnWidth(5, 100)  # 确认人
        self.violation_table.setColumnWidth(6, 80)   # 确认结果
        self.violation_table.setColumnWidth(7, 100)  # 操作

        # 创建高性能表格（用于大数据集）
        self.high_performance_table = HighPerformanceTableView(self)
        self.high_performance_table.setVisible(False)

        # 添加到布局
        layout.addWidget(self.violation_table)
        layout.addWidget(self.high_performance_table)

        return group_box
    
    def create_status_bar(self):
        """创建状态栏"""
        # 创建状态栏容器
        status_widget = QWidget()
        status_layout = QHBoxLayout(status_widget)
        status_layout.setContentsMargins(5, 2, 5, 2)
        
        # 添加状态信息
        self.status_label = QLabel("就绪")
        status_layout.addWidget(self.status_label)

        # 添加弹性空间
        status_layout.addStretch()

        # 添加统计信息
        self.stats_label = QLabel("总计: 0条违例 | 已确认: 0条 | 待确认: 0条")
        status_layout.addWidget(self.stats_label)

        # 添加性能信息
        self.perf_label = QLabel("")
        self.perf_label.setStyleSheet("color: #666; font-size: 11px;")
        status_layout.addWidget(self.perf_label)

        # 添加时间信息
        self.time_label = QLabel("")
        status_layout.addWidget(self.time_label)
        
        # 定时更新时间
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time_display)
        self.timer.start(1000)  # 每秒更新
        self.update_time_display()

        return status_widget

    def on_cell_double_clicked(self, row: int, column: int):
        """处理表格单元格双击事件"""
        # 只处理层级路径列（第1列，索引为1）
        if column == 1:
            item = self.violation_table.item(row, column)
            if item:
                hier_path = item.text()
                if hier_path:
                    # 跨平台剪贴板复制
                    success = self._copy_to_clipboard(hier_path)

                    if success:
                        # 显示成功提示消息
                        self.status_label.setText(f"已复制层级路径: {hier_path[:50]}{'...' if len(hier_path) > 50 else ''}")
                    else:
                        # 显示失败提示消息
                        self.status_label.setText(f"复制失败，请手动选择文本: {hier_path[:30]}...")

                    # 使用QTimer在3秒后恢复原状态文本
                    QTimer.singleShot(3000, self.restore_status_text)

    def _copy_to_clipboard(self, text: str) -> bool:
        """跨平台剪贴板复制功能

        Args:
            text: 要复制的文本

        Returns:
            bool: 复制是否成功
        """
        try:
            import platform
            system = platform.system().lower()

            # 方法1: 使用PyQt5剪贴板（主要方法）
            clipboard = QApplication.clipboard()

            # 在Linux下，尝试设置多种剪贴板模式
            if system == 'linux':
                # 设置主剪贴板（Ctrl+V粘贴）
                clipboard.setText(text, clipboard.Clipboard)
                # 设置选择剪贴板（鼠标中键粘贴）
                clipboard.setText(text, clipboard.Selection)

                # 验证复制是否成功
                clipboard_text = clipboard.text(clipboard.Clipboard)
                selection_text = clipboard.text(clipboard.Selection)

                if clipboard_text == text or selection_text == text:
                    print(f"Linux剪贴板复制成功: {text[:50]}...")
                    return True
                else:
                    print(f"Linux剪贴板复制验证失败")
                    # 尝试备用方法
                    return self._copy_to_clipboard_fallback(text)
            else:
                # Windows和macOS使用标准剪贴板
                clipboard.setText(text, clipboard.Clipboard)

                # 验证复制是否成功
                clipboard_text = clipboard.text(clipboard.Clipboard)
                if clipboard_text == text:
                    print(f"{system.title()}剪贴板复制成功: {text[:50]}...")
                    return True
                else:
                    print(f"{system.title()}剪贴板复制验证失败")
                    return False

        except Exception as e:
            print(f"剪贴板复制异常: {str(e)}")
            # 尝试备用方法
            return self._copy_to_clipboard_fallback(text)

    def _copy_to_clipboard_fallback(self, text: str) -> bool:
        """备用剪贴板复制方法（使用系统命令）

        Args:
            text: 要复制的文本

        Returns:
            bool: 复制是否成功
        """
        try:
            import platform
            import subprocess

            system = platform.system().lower()

            if system == 'linux':
                # 尝试使用xclip命令
                try:
                    # 复制到主剪贴板
                    subprocess.run(['xclip', '-selection', 'clipboard'],
                                 input=text.encode('utf-8'),
                                 check=True,
                                 timeout=2)
                    # 复制到选择剪贴板
                    subprocess.run(['xclip', '-selection', 'primary'],
                                 input=text.encode('utf-8'),
                                 check=True,
                                 timeout=2)
                    print(f"Linux xclip复制成功: {text[:50]}...")
                    return True
                except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
                    # 尝试使用xsel命令
                    try:
                        subprocess.run(['xsel', '--clipboard', '--input'],
                                     input=text.encode('utf-8'),
                                     check=True,
                                     timeout=2)
                        subprocess.run(['xsel', '--primary', '--input'],
                                     input=text.encode('utf-8'),
                                     check=True,
                                     timeout=2)
                        print(f"Linux xsel复制成功: {text[:50]}...")
                        return True
                    except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
                        print("Linux备用剪贴板工具不可用")
                        return False

            elif system == 'windows':
                # Windows使用clip命令
                try:
                    subprocess.run(['clip'],
                                 input=text.encode('utf-8'),
                                 check=True,
                                 timeout=2)
                    print(f"Windows clip复制成功: {text[:50]}...")
                    return True
                except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
                    print("Windows clip命令不可用")
                    return False

            elif system == 'darwin':  # macOS
                # macOS使用pbcopy命令
                try:
                    subprocess.run(['pbcopy'],
                                 input=text.encode('utf-8'),
                                 check=True,
                                 timeout=2)
                    print(f"macOS pbcopy复制成功: {text[:50]}...")
                    return True
                except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
                    print("macOS pbcopy命令不可用")
                    return False

            return False

        except Exception as e:
            print(f"备用剪贴板复制异常: {str(e)}")
            return False

    def restore_status_text(self):
        """恢复状态栏文本"""
        if hasattr(self, 'current_case_name') and self.current_case_name:
            self.update_progress_display()
        else:
            self.status_label.setText("就绪")

    def connect_signals(self):
        """连接信号槽"""
        # 数据模型信号 - 使用Qt.QueuedConnection避免死锁
        self.data_model.violation_added.connect(self.on_violation_added, Qt.QueuedConnection)
        self.data_model.violation_updated.connect(self.on_violation_updated, Qt.QueuedConnection)
        self.data_model.confirmation_updated.connect(self.on_confirmation_updated, Qt.QueuedConnection)

        # 界面信号
        self.corner_combo.currentTextChanged.connect(self.on_corner_changed)
        self.case_name_edit.textChanged.connect(self.on_case_name_changed)

        # 标准表格双击事件
        self.violation_table.cellDoubleClicked.connect(self.on_cell_double_clicked)

        # 高性能表格信号
        self.high_performance_table.cell_double_clicked.connect(self.on_high_performance_cell_double_clicked)
        # 注意：action_button_clicked信号已移除，现在使用直接的按钮点击处理

    def apply_runsim_theme(self):
        """应用RunSim GUI主题样式"""
        runsim_style = """
            /* 主窗口样式 - 与 Runsim GUI 保持一致 */
            QMainWindow, QDialog {
                background-color: #f5f5f5;
                color: #444;
            }

            /* 分组框样式 */
            QGroupBox {
                font-family: "Microsoft YaHei";
                font-weight: bold;
                border: 2px solid #d0d0d0;
                border-radius: 6px;
                margin-top: 12px;
                padding: 10px;
                background-color: white;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                left: 10px;
                padding: 0 5px;
                color: #444;
            }

            /* 按钮样式 */
            QPushButton {
                background-color: #4a9eff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
                font-family: "Microsoft YaHei";
                min-width: 80px;
            }

            QPushButton:hover {
                background-color: #3d8ced;
            }

            QPushButton:pressed {
                background-color: #3274bf;
            }

            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }

            /* 输入框样式 */
            QLineEdit {
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 5px;
                background: white;
                selection-background-color: #4a9eff;
                font-family: "Microsoft YaHei";
            }

            QLineEdit:focus {
                border: 2px solid #4a9eff;
            }

            /* 下拉框样式 */
            QComboBox {
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 5px;
                background: white;
                font-family: "Microsoft YaHei";
                min-width: 100px;
            }

            QComboBox:focus {
                border: 2px solid #4a9eff;
            }

            QComboBox::drop-down {
                border: none;
                width: 20px;
            }

            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666;
                margin-right: 5px;
            }

            /* 表格样式 */
            QTableWidget {
                background-color: white;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                gridline-color: #e0e0e0;
                font-family: "Microsoft YaHei";
                selection-background-color: #4a9eff;
                selection-color: white;
            }

            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #e0e0e0;
            }

            QTableWidget::item:hover {
                background-color: #f0f8ff;
            }

            QTableWidget::item:selected {
                background-color: #4a9eff;
                color: white;
            }

            QHeaderView::section {
                background-color: #f8f8f8;
                border: 1px solid #d0d0d0;
                padding: 5px;
                font-weight: bold;
                font-family: "Microsoft YaHei";
            }

            /* 进度条样式 */
            QProgressBar {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                text-align: center;
                font-family: "Microsoft YaHei";
            }

            QProgressBar::chunk {
                background-color: #4a9eff;
                border-radius: 3px;
            }

            /* 状态栏样式 */
            QStatusBar {
                background-color: #f0f0f0;
                border-top: 1px solid #d0d0d0;
                color: #666;
                font-family: "Microsoft YaHei";
            }

            /* 工具栏样式 */
            QToolBar {
                background-color: #f8f8f8;
                border-bottom: 1px solid #d0d0d0;
                spacing: 3px;
                padding: 3px;
            }

            QToolBar::separator {
                background-color: #d0d0d0;
                width: 1px;
                margin: 0 5px;
            }
        """
        self.setStyleSheet(runsim_style)

    def select_log_file(self):
        """选择日志文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择时序违例日志文件",
            "",
            "日志文件 (vio_summary.log);;所有文件 (*)"
        )

        if file_path:
            self.load_log_file(file_path)

    def load_log_file(self, file_path: str):
        """加载日志文件"""
        try:
            # 更新文件路径显示
            self.file_path_edit.setText(file_path)
            self.current_file_path = file_path

            # 解析用例信息
            dir_path = os.path.dirname(os.path.dirname(file_path))  # 去掉/log/vio_summary.log
            case_info = CaseInfoParser.parse_directory_name(dir_path)

            # 更新用例信息
            self.case_name_edit.setText(case_info['case_name'])
            self.current_case_name = case_info['case_name']

            if case_info['corner']:
                # 找到对应的corner并选中
                index = self.corner_combo.findText(case_info['corner'])
                if index >= 0:
                    self.corner_combo.setCurrentIndex(index)
                    self.current_corner = case_info['corner']
                    print(f"设置corner为: {self.current_corner}")
                else:
                    self.corner_combo.setCurrentIndex(0)
                    self.current_corner = ""
                    print("未找到匹配的corner，设置为空")
            else:
                self.corner_combo.setCurrentIndex(0)
                self.current_corner = ""
                print("没有corner信息，设置为空")

            # 异步解析文件
            self.parse_file_async(file_path)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载文件失败: {str(e)}")

    def parse_file_async(self, file_path: str):
        """智能异步解析文件"""
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)

        # 禁用相关按钮
        self.select_file_btn.setEnabled(False)
        self.auto_confirm_btn.setEnabled(False)

        # 智能选择解析器
        file_size = os.path.getsize(file_path)
        file_size_mb = file_size / (1024 * 1024)

        # 根据文件大小选择解析策略 - 进一步降低阈值，更早使用高性能模式
        if file_size_mb > 2:  # 大于2MB就使用高性能解析器，避免GUI卡死
            self.status_label.setText(f"检测到大文件 ({file_size_mb:.1f}MB)，使用高性能解析器...")
            self.async_parser = HighPerformanceAsyncParser(file_path)
        else:
            self.status_label.setText(f"使用标准解析器解析文件 ({file_size_mb:.1f}MB)...")
            self.async_parser = AsyncVioLogParser(file_path)

        # 连接信号
        self.async_parser.progress_updated.connect(self.on_parsing_progress)
        self.async_parser.parsing_completed.connect(self.on_parsing_completed)
        self.async_parser.parsing_failed.connect(self.on_parsing_failed)

        # 开始解析
        self.async_parser.start()

    def on_parsing_progress(self, progress: int, message: str):
        """解析进度更新"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(message)

    def on_parsing_completed(self, violations: List[Dict]):
        """解析完成（优化版本）"""
        try:
            # 隐藏进度条
            self.progress_bar.setVisible(False)

            # 启用按钮
            self.select_file_btn.setEnabled(True)
            self.auto_confirm_btn.setEnabled(True)

            # 保存违例数据
            self.current_violations = violations

            # 显示性能统计信息
            self._show_performance_stats(violations)

            # 大数据量使用异步处理
            if len(violations) > 1000:
                print(f"检测到大数据量({len(violations)}条)，启用异步后处理")
                self.start_async_post_processing(violations)
            else:
                # 小数据量使用同步处理
                self.process_violations_sync(violations)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"处理解析结果失败: {str(e)}")

    def start_async_post_processing(self, violations: List[Dict]):
        """启动异步后处理"""
        try:
            # 创建异步数据处理器
            self.async_processor = AsyncDataProcessor(
                violations, self.current_case_name,
                self.get_stored_corner(), self.current_file_path, self.data_model
            )

            # 连接信号
            self.async_processor.stage_completed.connect(self.on_processing_stage_completed)
            self.async_processor.processing_finished.connect(self.on_processing_finished)
            self.async_processor.processing_failed.connect(self.on_processing_failed)

            # 显示进度
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(98, 100)
            self.status_label.setText("后台处理中...")

            # 启动异步处理
            self.async_processor.start()

        except Exception as e:
            print(f"启动异步后处理失败: {e}")
            # 回退到同步处理
            self.process_violations_sync(violations)

    def process_violations_sync(self, violations: List[Dict]):
        """同步处理违例数据（小数据量）"""
        try:
            # 清除旧数据
            if self.current_case_name:
                stored_corner = self.get_stored_corner()
                self.data_model.clear_case_data(self.current_case_name, stored_corner)

            # 添加新数据到数据库
            if violations and self.current_case_name:
                stored_corner = self.get_stored_corner()
                success_count = self.data_model.add_violations(
                    violations, self.current_case_name, stored_corner, self.current_file_path
                )
                print(f"成功添加 {success_count} 条违例记录到数据库")

                # 自动应用历史确认记录
                applied_count = self.data_model.apply_historical_confirmations(
                    self.current_case_name, stored_corner
                )
                if applied_count > 0:
                    self.status_label.setText(f"解析完成，共 {len(violations)} 条违例记录，自动应用历史确认 {applied_count} 条")
                else:
                    self.status_label.setText(f"解析完成，共 {len(violations)} 条违例记录")

            # 清除查询缓存，确保获取最新数据
            if hasattr(self.data_model, 'clear_cache'):
                self.data_model.clear_cache()
                print("数据加载完成，已清除查询缓存")

            # 更新表格显示
            self.update_violation_table()

            # 更新进度显示
            self.update_progress_display()

            # 更新性能信息显示
            self.update_performance_display()

        except Exception as e:
            print(f"同步处理违例数据失败: {e}")

    def on_processing_stage_completed(self, stage_name: str, progress: int):
        """处理阶段完成"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(f"{stage_name}...")

    def on_processing_finished(self, result_data: dict):
        """异步处理完成"""
        try:
            # 隐藏进度条
            self.progress_bar.setVisible(False)

            # 更新状态
            success_count = result_data.get('success_count', 0)
            applied_count = result_data.get('applied_count', 0)

            if applied_count > 0:
                self.status_label.setText(f"解析完成，共 {len(self.current_violations)} 条违例记录，自动应用历史确认 {applied_count} 条")
            else:
                self.status_label.setText(f"解析完成，共 {len(self.current_violations)} 条违例记录")

            # 更新表格显示
            self.update_violation_table()

            # 更新进度显示
            self.update_progress_display()

            # 更新性能信息显示
            self.update_performance_display()

        except Exception as e:
            print(f"处理异步完成事件失败: {e}")

    def on_processing_failed(self, error_message: str):
        """异步处理失败"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("后台处理失败")
        print(f"异步处理失败: {error_message}")

        # 回退到同步处理
        try:
            self.process_violations_sync(self.current_violations)
        except Exception as e:
            print(f"回退到同步处理也失败: {e}")

    def _show_performance_stats(self, violations: List[Dict]):
        """显示性能统计信息"""
        try:
            # 获取解析器统计信息
            if hasattr(self.async_parser, 'parser') and hasattr(self.async_parser.parser, 'parse_stats'):
                stats = self.async_parser.parser.parse_stats
                total_lines = stats.get('total_lines', 0)
                parse_time = stats.get('parse_time', 0)

                if parse_time > 0:
                    throughput = len(violations) / parse_time
                    lines_per_sec = total_lines / parse_time

                    print(f"=== 解析性能统计 ===")
                    print(f"总行数: {total_lines:,}")
                    print(f"违例记录: {len(violations):,}")
                    print(f"解析时间: {parse_time:.2f}秒")
                    print(f"处理速度: {lines_per_sec:.0f} 行/秒")
                    print(f"违例吞吐量: {throughput:.0f} 记录/秒")

                    # 性能建议
                    if parse_time > 10:
                        print(f"性能建议: 文件较大，建议考虑分批处理或使用SSD存储")
                    elif throughput < 1000:
                        print(f"性能建议: 吞吐量较低，可能受到磁盘I/O限制")

        except Exception as e:
            print(f"显示性能统计失败: {str(e)}")    
    
    def update_performance_display(self):
        """更新性能信息显示"""
        try:
            stats = self.performance_stats
            if stats['last_record_count'] > 0:
                load_time = stats['last_load_time']
                record_count = stats['last_record_count']
                memory_mb = stats['memory_usage_mb']

                # 计算性能指标
                throughput = record_count / max(load_time, 0.001)  # 避免除零

                # 生成性能信息
                perf_info = f"加载: {load_time:.2f}s | 吞吐量: {throughput:.0f} 记录/s"
                if memory_mb > 0:
                    perf_info += f" | 内存: {memory_mb:.1f}MB"

                # 性能建议
                suggestions = self._get_performance_suggestions(load_time, record_count, memory_mb)
                if suggestions:
                    perf_info += f" | 建议: {suggestions}"

                self.perf_label.setText(perf_info)
            else:
                self.perf_label.setText("")
        except Exception as e:
            print(f"更新性能显示失败: {str(e)}")

    def _get_performance_suggestions(self, load_time: float, record_count: int, memory_mb: float) -> str:
        """获取性能优化建议"""
        suggestions = []

        # 加载时间建议
        if load_time > 5.0:
            suggestions.append("加载较慢，建议使用筛选功能")
        elif load_time > 2.0:
            suggestions.append("可考虑分批处理")

        # 记录数量建议
        if record_count > 10000:
            suggestions.append("数据量大，建议使用高性能模式")
        elif record_count > 5000:
            suggestions.append("建议启用分页显示")

        # 内存使用建议
        if memory_mb > 500:
            suggestions.append("内存占用较高")

        return " | ".join(suggestions[:2])  # 最多显示2个建议，避免状态栏过长

    def on_parsing_failed(self, error_message: str):
        """解析失败"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 启用按钮
        self.select_file_btn.setEnabled(True)

        # 显示错误 - 使用安全消息框，避免弹窗
        self.status_label.setText("解析失败")
        print(f"解析失败: {error_message}")

        # 在状态栏显示错误信息而不是弹窗
        self.status_label.setText(f"解析失败: {error_message[:50]}...")
        QTimer.singleShot(5000, lambda: self.status_label.setText("就绪"))

    def update_violation_table(self):
        """更新违例表格显示（性能优化版本）"""
        # 开始性能监控
        operation_id = self.performance_monitor.start_operation("update_violation_table")

        try:
            if not self.current_case_name:
                self._clear_all_tables()
                return

            # 从数据库获取违例记录（带缓存）
            stored_corner = self.get_stored_corner()
            print(f"查询违例记录 - 用例: {self.current_case_name}, 存储corner: {stored_corner}")

            # 使用缓存查询
            query_operation_id = self.performance_monitor.start_operation("database_query")
            violations = self._get_violations_with_cache(self.current_case_name, stored_corner)
            self.performance_monitor.end_operation(query_operation_id, 'query')

            print(f"找到 {len(violations)} 条违例记录")

            # 记录内存使用
            try:
                import psutil
                process = psutil.Process()
                memory_mb = process.memory_info().rss / 1024 / 1024
                self.performance_monitor.record_memory_usage(memory_mb)
            except ImportError:
                # psutil 不可用时使用默认值
                self.performance_monitor.record_memory_usage(100)

            # 自适应选择表格类型
            threshold = self._get_adaptive_threshold()
            if len(violations) > threshold:
                self._use_high_performance_table(violations)
            else:
                self._use_standard_table(violations)

            # 更新进度显示
            self.update_progress_display()

            # 更新统计信息
            self.update_stats_display()

            # 记录缓存性能
            if hasattr(self.data_model, 'get_cache_stats'):
                cache_stats = self.data_model.get_cache_stats()
                if cache_stats['cache_hits'] + cache_stats['cache_misses'] > 0:
                    hit_rate = cache_stats['hit_rate']
                    self.performance_monitor.record_cache_performance(hit_rate)

        except Exception as e:
            print(f"更新违例表格失败: {e}")
        finally:
            # 结束性能监控
            self.performance_monitor.end_operation(operation_id, 'ui')

    def _get_memory_usage(self):
        """获取当前内存使用量（MB）"""
        try:
            import psutil
            import os
            process = psutil.Process(os.getpid())
            return process.memory_info().rss / 1024 / 1024
        except ImportError:
            return 0

    def _get_violations_with_cache(self, case_name: str, corner: str) -> list:
        """使用缓存获取违例记录"""
        if hasattr(self.data_model, '_get_cache_key'):
            cache_key = self.data_model._get_cache_key('violations_by_case',
                                                      case_name=case_name, corner=corner)
            cached_data = self.data_model._get_from_cache(cache_key)
            if cached_data is not None:
                return cached_data

            # 缓存未命中，查询数据库
            violations = self.data_model.get_violations_by_case(case_name, corner)
            self.data_model._set_cache(cache_key, violations)
            return violations
        else:
            # 回退到原始方法
            return self.data_model.get_violations_by_case(case_name, corner)

    def _get_adaptive_threshold(self) -> int:
        """获取自适应性能阈值"""
        try:
            performance_summary = self.performance_monitor.get_performance_summary()

            # 基于历史性能调整阈值
            avg_memory = performance_summary.get('avg_memory_mb', 0)
            avg_ui_time = performance_summary.get('avg_ui_time', 0)

            base_threshold = self.performance_threshold

            # 内存使用高时降低阈值
            if avg_memory > 300:
                base_threshold = max(200, base_threshold - 100)
            elif avg_memory > 200:
                base_threshold = max(300, base_threshold - 50)

            # UI响应慢时降低阈值
            if avg_ui_time > 0.2:
                base_threshold = max(200, base_threshold - 100)
            elif avg_ui_time > 0.1:
                base_threshold = max(300, base_threshold - 50)

            return base_threshold

        except Exception as e:
            print(f"获取自适应阈值失败: {e}")
            return self.performance_threshold

    def _clear_all_tables(self):
        """清空所有表格"""
        self.violation_table.setRowCount(0)
        self.high_performance_table.update_data([])
        self.performance_info_label.setVisible(False)

    def _use_high_performance_table(self, violations):
        """使用高性能表格显示大数据集"""
        print(f"使用高性能表格显示 {len(violations)} 条记录")

        # 隐藏标准表格，显示高性能表格
        self.violation_table.setVisible(False)
        self.high_performance_table.setVisible(True)

        # 显示性能提示
        self.performance_info_label.setText(
            f"检测到大数据集({len(violations)}条记录)，已启用高性能模式以提升加载速度"
        )
        self.performance_info_label.setVisible(True)

        # 更新高性能表格数据
        self.high_performance_table.update_data(violations)
        self.use_high_performance_table = True

    def _use_standard_table(self, violations):
        """使用标准表格显示小数据集"""
        print(f"使用标准表格显示 {len(violations)} 条记录")

        # 显示标准表格，隐藏高性能表格
        self.violation_table.setVisible(True)
        self.high_performance_table.setVisible(False)
        self.performance_info_label.setVisible(False)

        # 设置表格行数
        self.violation_table.setRowCount(len(violations))
        self.use_high_performance_table = False

        # 填充标准表格数据
        self._fill_standard_table(violations)

    def _fill_standard_table(self, violations):
        """填充标准表格数据（优化版本）"""
        import time
        start_time = time.time()

        # 批量处理：预先计算所有需要的数据，减少重复计算
        processed_data = []
        for violation in violations:
            status = violation.get('status', 'pending')
            is_confirmed = status in ['confirmed', 'ignored']
            time_ns = violation.get('time_fs', 0) / 1000000

            processed_data.append({
                'violation': violation,
                'status': status,
                'is_confirmed': is_confirmed,
                'time_ns': time_ns
            })

        # 定义已确认条目的灰色样式
        gray_text_color = QColor(128, 128, 128)  # 灰色文字
        normal_text_color = QColor(0, 0, 0)      # 正常黑色文字

        # 批量设置表格数据，减少单次操作开销
        for row, data in enumerate(processed_data):
            violation = data['violation']
            status = data['status']
            is_confirmed = data['is_confirmed']
            time_ns = data['time_ns']

            # NUM
            num_item = QTableWidgetItem(str(violation.get('num', '')))
            num_item.setTextAlignment(Qt.AlignCenter)
            if is_confirmed:
                num_item.setForeground(gray_text_color)
            self.violation_table.setItem(row, 0, num_item)

            # 层级路径
            hier_text = violation.get('hier', '')
            hier_item = QTableWidgetItem(hier_text)
            hier_item.setToolTip(hier_text)  # 添加悬停提示
            if is_confirmed:
                hier_item.setForeground(gray_text_color)
            self.violation_table.setItem(row, 1, hier_item)

            # 时间(ns)
            time_ns = violation.get('time_fs', 0) / 1000000
            time_item = QTableWidgetItem(f"{time_ns:.3f}")
            time_item.setTextAlignment(Qt.AlignCenter)
            if is_confirmed:
                time_item.setForeground(gray_text_color)
            self.violation_table.setItem(row, 2, time_item)

            # 检查信息
            check_info_text = violation.get('check_info', '')
            check_item = QTableWidgetItem(check_info_text)
            check_item.setToolTip(check_info_text)  # 添加悬停提示
            if is_confirmed:
                check_item.setForeground(gray_text_color)
            self.violation_table.setItem(row, 3, check_item)

            # 状态
            status_item = QTableWidgetItem(self.get_status_display(status))
            status_item.setTextAlignment(Qt.AlignCenter)

            # 设置状态颜色（保持原有的背景色，但文字使用对应颜色）
            if status == 'confirmed':
                status_item.setBackground(QColor(144, 238, 144))  # 浅绿色背景
                status_item.setForeground(QColor(0, 100, 0))     # 深绿色文字
            elif status == 'ignored':
                status_item.setBackground(QColor(255, 182, 193))  # 浅红色背景
                status_item.setForeground(QColor(139, 0, 0))     # 深红色文字
            else:
                status_item.setBackground(QColor(255, 255, 224))  # 浅黄色背景
                status_item.setForeground(QColor(184, 134, 11))  # 深黄色文字

            self.violation_table.setItem(row, 4, status_item)

            # 确认人
            confirmer_item = QTableWidgetItem(violation.get('confirmer', ''))
            confirmer_item.setTextAlignment(Qt.AlignCenter)
            if is_confirmed:
                confirmer_item.setForeground(gray_text_color)
            self.violation_table.setItem(row, 5, confirmer_item)

            # 确认结果
            result = violation.get('result', '')
            result_item = QTableWidgetItem(self.get_result_display(result))
            result_item.setTextAlignment(Qt.AlignCenter)
            if is_confirmed:
                result_item.setForeground(gray_text_color)
            self.violation_table.setItem(row, 6, result_item)

            # 操作按钮
            if status == 'pending':
                confirm_btn = QPushButton("确认")
                confirm_btn.setMaximumWidth(80)
                # 使用安全的按钮连接方式，避免lambda闭包问题
                confirm_btn.setProperty('violation_id', violation.get('id'))
                confirm_btn.setProperty('action_type', 'pending')
                confirm_btn.clicked.connect(self.handle_standard_table_button_click)
                self.violation_table.setCellWidget(row, 7, confirm_btn)
            else:
                edit_btn = QPushButton("编辑")
                edit_btn.setMaximumWidth(80)
                # 为已确认条目的按钮也应用灰色样式
                edit_btn.setStyleSheet("""
                    QPushButton {
                        color: #808080;
                        background-color: #f0f0f0;
                        border: 1px solid #c0c0c0;
                    }
                    QPushButton:hover {
                        background-color: #e0e0e0;
                    }
                """)
                # 使用安全的按钮连接方式，避免lambda闭包问题
                edit_btn.setProperty('violation_id', violation.get('id'))
                edit_btn.setProperty('action_type', 'confirmed')
                edit_btn.clicked.connect(self.handle_standard_table_button_click)
                self.violation_table.setCellWidget(row, 7, edit_btn)

    def on_high_performance_cell_double_clicked(self, row, column):
        """处理高性能表格单元格双击事件"""
        # 只处理层级路径列（第1列，索引为1）
        if column == 1:
            # 获取实际行索引（考虑分页偏移）
            actual_row = self.high_performance_table.current_page * self.high_performance_table.page_size + row
            violation = self.high_performance_table.model.get_violation_at_row(actual_row)

            if violation:
                hier_path = violation.get('hier', '')
                if hier_path:
                    # 跨平台剪贴板复制
                    success = self._copy_to_clipboard(hier_path)

                    if success:
                        # 显示成功提示消息
                        self.status_label.setText(f"已复制层级路径: {hier_path[:50]}{'...' if len(hier_path) > 50 else ''}")
                    else:
                        # 显示失败提示消息
                        self.status_label.setText(f"复制失败，请手动选择文本: {hier_path[:30]}...")

                    # 使用QTimer在3秒后恢复原状态文本
                    QTimer.singleShot(3000, self.restore_status_text)

    # 注意：on_high_performance_action_clicked 方法已被移除
    # 现在使用 handle_action_button_click 方法直接处理按钮点击

    def get_stored_corner(self) -> str:
        """获取数据存储时使用的corner

        Returns:
            str: 存储时使用的corner名称
        """
        # 如果用户已经选择了具体的corner，优先使用用户选择的corner
        if self.current_corner:
            return self.current_corner

        # 如果当前文件路径中包含corner信息，使用解析出的corner
        if self.current_file_path:
            dir_path = os.path.dirname(os.path.dirname(self.current_file_path))
            case_info = CaseInfoParser.parse_directory_name(dir_path)
            if case_info['corner']:
                return case_info['corner']

        # 否则使用default
        return "default"

    def get_display_corner(self) -> str:
        """获取显示用的corner名称（用于文件名等）

        Returns:
            str: 显示用的corner名称
        """
        if self.current_corner:
            return self.current_corner

        # 如果没有选择corner，使用存储的corner
        stored_corner = self.get_stored_corner()
        return stored_corner if stored_corner != "default" else "default"

    def get_status_display(self, status: str) -> str:
        """获取状态显示文本"""
        status_map = {
            'pending': '待确认',
            'confirmed': '已确认',
            'ignored': '已忽略'
        }
        return status_map.get(status, status)

    def get_result_display(self, result: str) -> str:
        """获取结果显示文本"""
        result_map = {
            'pass': '通过',
            'issue': '有问题',
            '': ''
        }
        return result_map.get(result, result)

    def auto_confirm_violations(self):
        """自动确认违例"""
        if not self.current_case_name:
            QMessageBox.warning(self, "警告", "请先选择日志文件")
            return

        # 获取复位时间
        try:
            reset_time_text = self.reset_time_edit.text().strip()
            if not reset_time_text:
                QMessageBox.warning(self, "警告", "请输入复位时间")
                return

            reset_time_ns = float(reset_time_text)
            if reset_time_ns <= 0:
                QMessageBox.warning(self, "警告", "复位时间必须大于0")
                return
        except ValueError:
            QMessageBox.warning(self, "警告", "复位时间格式错误，请输入数字")
            return

        # 执行自动确认
        stored_corner = self.get_stored_corner()
        print(f"自动确认违例 - 用例: {self.current_case_name}, 存储corner: {stored_corner}, 复位时间: {reset_time_ns}ns")

        confirmed_count = self.data_model.auto_confirm_by_reset_time(
            self.current_case_name, stored_corner, reset_time_ns
        )

        if confirmed_count > 0:
            # 清除缓存，确保UI刷新时获取最新数据
            if hasattr(self.data_model, 'clear_cache'):
                self.data_model.clear_cache()
                print("自动确认完成，已清除查询缓存")

            QMessageBox.information(self, "成功", f"已自动确认 {confirmed_count} 条违例记录")

            # 强制刷新UI
            self.update_violation_table()
            self.update_progress_display()
        else:
            QMessageBox.information(self, "提示", "没有找到需要自动确认的违例记录")

    def handle_standard_table_button_click(self):
        """处理标准表格中的按钮点击（安全版本）"""
        try:
            # 获取发送信号的按钮
            button = self.sender()
            if not button:
                return

            # 从按钮属性中获取数据
            violation_id = button.property('violation_id')
            action_type = button.property('action_type')

            # 验证数据有效性
            if not violation_id:
                print(f"标准表格按钮缺少violation_id属性")
                return

            # 根据操作类型执行相应操作
            if action_type == 'pending':
                self.confirm_single_violation(violation_id)
            else:
                self.edit_confirmation(violation_id)

        except Exception as e:
            # 静默处理错误，绝对不显示弹窗
            print(f"处理标准表格按钮点击失败: {e}")
            if hasattr(self, 'status_label'):
                self.status_label.setText(f"操作失败")
                QTimer.singleShot(2000, lambda: self.status_label.setText("就绪"))

    def confirm_single_violation(self, violation_id: int):
        """确认单个违例"""
        if not violation_id:
            return

        try:
            # 获取违例信息 - 使用正确的查询逻辑
            stored_corner = self.get_stored_corner()
            print(f"确认违例记录 - ID: {violation_id}, 用例: {self.current_case_name}, 存储corner: {stored_corner}")

            # 直接通过ID查询违例记录
            violation = self.data_model.get_violation_by_id(violation_id)

            if not violation:
                # 如果直接查询失败，尝试从当前显示的数据中查找
                violations = self.data_model.get_violations_by_case(self.current_case_name, stored_corner)
                violation = next((v for v in violations if v.get('id') == violation_id), None)

            if not violation:
                # 静默处理，避免弹窗
                print(f"确认时找不到违例记录，ID: {violation_id}")
                if hasattr(self, 'status_label'):
                    self.status_label.setText("找不到违例记录")
                    QTimer.singleShot(3000, lambda: self.status_label.setText("就绪"))
                return

            print(f"找到违例记录: {violation.get('hier', 'N/A')[:50]}...")

            # 检查历史建议
            suggestions = self.data_model.get_pattern_suggestions(
                violation.get('hier', ''), violation.get('check_info', '')
            )

            # 显示确认对话框
            dialog = ConfirmationDialog(self, violation, suggestions)
            dialog.setModal(True)  # 确保模态

            # 使用exec_()而不是show()
            result_code = dialog.exec_()

            if result_code == QDialog.Accepted:
                result = dialog.get_result()

                # 更新确认记录
                update_result = self.data_model.update_confirmation(
                    violation_id,
                    status='confirmed',
                    confirmer=result['confirmer'],
                    result=result['result'],
                    reason=result['reason'],
                    is_auto=False
                )

                # 处理返回结果
                if isinstance(update_result, tuple):
                    success, confirmation_data = update_result
                else:
                    success = update_result
                    confirmation_data = None

                if success:
                    # 保存到历史模式
                    self.data_model.save_pattern(
                        violation.get('hier', ''),
                        violation.get('check_info', ''),
                        result['confirmer'],
                        result['result'],
                        result['reason']
                    )

                    # 清除缓存，确保下次查询获取最新数据
                    if hasattr(self.data_model, 'clear_cache'):
                        self.data_model.clear_cache()
                        print("确认操作完成，已清除查询缓存")

                    # 使用QTimer延迟更新UI，避免死锁
                    QTimer.singleShot(0, self.safe_update_ui)

                    # 显示成功消息
                    self.status_label.setText("确认记录已更新")
                else:
                    QMessageBox.critical(self, "错误", "更新确认记录失败")

            # 确保对话框被正确销毁
            dialog.deleteLater()

        except Exception as e:
            print(f"确认违例时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"确认过程中出现错误: {str(e)}")

    def update_progress_display(self):
        """更新进度显示"""
        if not self.current_case_name:
            self.progress_label.setText("进度: 已确认 0/0 (0%)")
            self.stats_label.setText("总计: 0条违例 | 已确认: 0条 | 待确认: 0条")
            return

        # 获取统计信息
        stored_corner = self.get_stored_corner()
        violations = self.data_model.get_violations_by_case(self.current_case_name, stored_corner)

        total_count = len(violations)
        confirmed_count = sum(1 for v in violations if v.get('status') in ['confirmed', 'ignored'])
        pending_count = total_count - confirmed_count

        # 计算百分比
        percentage = (confirmed_count / total_count * 100) if total_count > 0 else 0

        # 更新显示
        self.progress_label.setText(f"进度: 已确认 {confirmed_count}/{total_count} ({percentage:.1f}%)")
        self.stats_label.setText(f"总计: {total_count}条违例 | 已确认: {confirmed_count}条 | 待确认: {pending_count}条")

    def update_time_display(self):
        """更新时间显示"""
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(f"最后更新: {current_time}")

    def update_stats_display(self):
        """更新统计信息显示"""
        try:
            if not self.current_case_name:
                self.stats_label.setText("总计: 0条违例 | 已确认: 0条 | 待确认: 0条")
                return

            # 获取统计数据
            stored_corner = self.get_stored_corner()
            violations = self.data_model.get_violations_by_case(self.current_case_name, stored_corner)

            total_count = len(violations)
            confirmed_count = sum(1 for v in violations if v.get('status') in ['confirmed', 'ignored'])
            pending_count = total_count - confirmed_count

            # 更新统计标签
            self.stats_label.setText(f"总计: {total_count}条违例 | 已确认: {confirmed_count}条 | 待确认: {pending_count}条")

            # 更新性能信息
            if hasattr(self, 'perf_label'):
                cache_info = ""
                if hasattr(self.data_model, 'get_cache_stats'):
                    cache_stats = self.data_model.get_cache_stats()
                    if cache_stats['cache_hits'] + cache_stats['cache_misses'] > 0:
                        cache_info = f" | 缓存命中率: {cache_stats['hit_rate']:.1f}%"

                memory_info = ""
                if hasattr(self, 'performance_monitor'):
                    summary = self.performance_monitor.get_performance_summary()
                    if 'avg_memory_mb' in summary:
                        memory_info = f" | 内存: {summary['avg_memory_mb']:.1f}MB"

                if cache_info or memory_info:
                    self.perf_label.setText(f"性能{cache_info}{memory_info}")

        except Exception as e:
            print(f"更新统计信息失败: {e}")
            self.stats_label.setText("统计信息更新失败")

    def safe_update_ui(self):
        """安全的UI更新方法，避免死锁"""
        try:
            self.update_violation_table()
            self.update_progress_display()
        except Exception as e:
            print(f"UI更新失败: {str(e)}")
            import traceback
            traceback.print_exc()

    # 信号槽方法
    def on_violation_added(self, violation_data: Dict):
        """违例添加信号处理"""
        pass  # 在批量添加时不需要单独处理

    def on_violation_updated(self, violation_data: Dict):
        """违例更新信号处理"""
        # 使用QTimer延迟更新，避免死锁
        QTimer.singleShot(0, self.safe_update_ui)

    def on_confirmation_updated(self, confirmation_data: Dict):
        """确认更新信号处理"""
        # 使用QTimer延迟更新，避免死锁
        QTimer.singleShot(0, self.safe_update_ui)

    def on_corner_changed(self, corner: str):
        """Corner选择改变 - 更新corner标记，如果是从default切换则更新数据库"""
        print(f"Corner选择改变: '{corner}'")

        # 更新当前corner标记
        old_corner = self.current_corner
        if corner != "请选择...":
            self.current_corner = corner
        else:
            self.current_corner = ""

        print(f"Corner标记从 '{old_corner}' 变更为 '{self.current_corner}'")

        # 如果有用例数据，检查是否需要更新数据库中的corner
        if self.current_case_name and self.current_corner:
            stored_corner = self.get_stored_corner()

            # 如果当前存储的是default，且用户选择了具体corner，则更新数据库
            if stored_corner == "default" and self.current_corner != "default":
                print(f"检测到从default切换到具体corner，更新数据库...")
                success = self.data_model.update_case_corner(
                    self.current_case_name, "default", self.current_corner
                )

                if success:
                    print(f"成功更新数据库corner从 'default' 到 '{self.current_corner}'")
                    # 更新文件路径以反映新的corner
                    if self.current_file_path:
                        # 重新构造文件路径以包含corner信息
                        dir_path = os.path.dirname(os.path.dirname(self.current_file_path))
                        new_dir_path = f"{dir_path}_{self.current_corner}"
                        self.current_file_path = os.path.join(new_dir_path, "log", "vio_summary.log")
                        print(f"更新文件路径: {self.current_file_path}")
                else:
                    print("更新数据库corner失败")

        # 更新进度显示
        if self.current_case_name:
            self.update_progress_display()

        print("Corner切换完成")

    def on_case_name_changed(self, case_name: str):
        """用例名称改变"""
        self.current_case_name = case_name.strip()
        if self.current_case_name:
            self.update_violation_table()
            self.update_progress_display()

    def batch_confirm_violations(self):
        """批量确认违例"""
        if self.use_high_performance_table:
            QMessageBox.information(
                self, "提示",
                "高性能模式下暂不支持批量确认功能，请使用单个确认或全部确认功能。"
            )
            return

        # 获取选中的行
        selected_rows = set()
        for item in self.violation_table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            QMessageBox.warning(self, "警告", "请先选择要确认的违例记录")
            return

        # 显示批量确认对话框
        dialog = BatchConfirmationDialog(self)
        dialog.setModal(True)
        result_code = dialog.exec_()

        if result_code == QDialog.Accepted:
            result = dialog.get_result()

            # 获取选中违例的ID
            stored_corner = self.get_stored_corner()
            print(f"批量确认违例 - 用例: {self.current_case_name}, 存储corner: {stored_corner}")
            violations = self.data_model.get_violations_by_case(self.current_case_name, stored_corner)

            success_count = 0
            for row in selected_rows:
                if row < len(violations):
                    violation = violations[row]
                    violation_id = violation.get('id')

                    if violation.get('status') == 'pending':
                        update_result = self.data_model.update_confirmation(
                            violation_id,
                            status='confirmed',
                            confirmer=result['confirmer'],
                            result=result['result'],
                            reason=result['reason'],
                            is_auto=False
                        )

                        # 处理返回结果
                        if isinstance(update_result, tuple):
                            success, _ = update_result
                        else:
                            success = update_result

                        if success:
                            success_count += 1
                            # 保存到历史模式
                            self.data_model.save_pattern(
                                violation.get('hier', ''),
                                violation.get('check_info', ''),
                                result['confirmer'],
                                result['result'],
                                result['reason']
                            )

            if success_count > 0:
                # 清除缓存，确保UI刷新时获取最新数据
                if hasattr(self.data_model, 'clear_cache'):
                    self.data_model.clear_cache()
                    print("批量确认完成，已清除查询缓存")

                QMessageBox.information(self, "成功", f"已确认 {success_count} 条违例记录")

                # 强制刷新UI
                self.update_violation_table()
                self.update_progress_display()
            else:
                QMessageBox.warning(self, "警告", "没有可确认的记录")

        # 确保对话框被正确销毁
        dialog.deleteLater()

    def confirm_all_violations(self):
        """确认所有违例"""
        if not self.current_case_name:
            QMessageBox.warning(self, "警告", "请先选择日志文件")
            return

        # 获取待确认的违例数量
        stored_corner = self.get_stored_corner()
        print(f"全部确认违例 - 用例: {self.current_case_name}, 存储corner: {stored_corner}")

        violations = self.data_model.get_violations_by_case(self.current_case_name, stored_corner)
        pending_violations = [v for v in violations if v.get('status') == 'pending']

        if not pending_violations:
            QMessageBox.information(self, "提示", "没有待确认的违例记录")
            return

        # 确认操作
        reply = QMessageBox.question(
            self, "确认",
            f"确定要确认所有 {len(pending_violations)} 条待确认的违例记录吗？",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 显示批量确认对话框
            dialog = BatchConfirmationDialog(self)
            dialog.setModal(True)
            result_code = dialog.exec_()

            if result_code == QDialog.Accepted:
                result = dialog.get_result()

                success_count = 0
                for violation in pending_violations:
                    violation_id = violation.get('id')
                    update_result = self.data_model.update_confirmation(
                        violation_id,
                        status='confirmed',
                        confirmer=result['confirmer'],
                        result=result['result'],
                        reason=result['reason'],
                        is_auto=False
                    )

                    # 处理返回结果
                    if isinstance(update_result, tuple):
                        success, _ = update_result
                    else:
                        success = update_result

                    if success:
                        success_count += 1
                        # 保存到历史模式
                        self.data_model.save_pattern(
                            violation.get('hier', ''),
                            violation.get('check_info', ''),
                            result['confirmer'],
                            result['result'],
                            result['reason']
                        )

                if success_count > 0:
                    # 清除缓存，确保UI刷新时获取最新数据
                    if hasattr(self.data_model, 'clear_cache'):
                        self.data_model.clear_cache()
                        print("全部确认完成，已清除查询缓存")

                    QMessageBox.information(self, "成功", f"已确认 {success_count} 条违例记录")

                    # 强制刷新UI
                    self.update_violation_table()
                    self.update_progress_display()

            # 确保对话框被正确销毁
            dialog.deleteLater()

    def edit_confirmation(self, violation_id: int):
        """编辑确认记录"""
        if not violation_id:
            return

        try:
            # 获取违例信息 - 使用正确的查询逻辑
            stored_corner = self.get_stored_corner()
            print(f"编辑违例记录 - ID: {violation_id}, 用例: {self.current_case_name}, 存储corner: {stored_corner}")

            # 直接通过ID查询违例记录
            violation = self.data_model.get_violation_by_id(violation_id)

            if not violation:
                # 如果直接查询失败，尝试从当前显示的数据中查找
                violations = self.data_model.get_violations_by_case(self.current_case_name, stored_corner)
                violation = next((v for v in violations if v.get('id') == violation_id), None)

            if not violation:
                # 静默处理，避免弹窗
                print(f"编辑时找不到违例记录，ID: {violation_id}")
                if hasattr(self, 'status_label'):
                    self.status_label.setText("找不到违例记录")
                    QTimer.singleShot(3000, lambda: self.status_label.setText("就绪"))
                return

            print(f"找到违例记录: {violation.get('hier', 'N/A')[:50]}...")

            # 显示编辑对话框
            dialog = ConfirmationDialog(self, violation, None, edit_mode=True)
            dialog.setModal(True)  # 确保模态

            result_code = dialog.exec_()

            if result_code == QDialog.Accepted:
                result = dialog.get_result()

                # 更新确认记录
                update_result = self.data_model.update_confirmation(
                    violation_id,
                    status='confirmed',
                    confirmer=result['confirmer'],
                    result=result['result'],
                    reason=result['reason'],
                    is_auto=False
                )

                # 处理返回结果
                if isinstance(update_result, tuple):
                    success, _ = update_result
                else:
                    success = update_result

                if success:
                    # 更新历史模式
                    self.data_model.save_pattern(
                        violation.get('hier', ''),
                        violation.get('check_info', ''),
                        result['confirmer'],
                        result['result'],
                        result['reason']
                    )

                    # 清除缓存，确保下次查询获取最新数据
                    if hasattr(self.data_model, 'clear_cache'):
                        self.data_model.clear_cache()
                        print("编辑操作完成，已清除查询缓存")

                    # 使用QTimer延迟更新UI，避免死锁
                    QTimer.singleShot(0, self.safe_update_ui)

                    # 显示成功消息
                    self.status_label.setText("确认记录已更新")
                else:
                    QMessageBox.critical(self, "错误", "更新确认记录失败")

            # 确保对话框被正确销毁
            dialog.deleteLater()

        except Exception as e:
            print(f"编辑确认时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"编辑过程中出现错误: {str(e)}")

    def refresh_data(self):
        """刷新数据"""
        if self.current_file_path:
            self.load_log_file(self.current_file_path)
        else:
            self.update_violation_table()
            self.update_progress_display()

    def export_to_excel(self):
        """导出为Excel文件"""
        if not self.current_case_name:
            QMessageBox.warning(self, "警告", "没有数据可导出")
            return

        # 检查表格中是否有数据
        table_row_count = self.violation_table.rowCount()
        print(f"导出Excel - 表格中有 {table_row_count} 行数据")

        if table_row_count == 0:
            QMessageBox.warning(self, "警告", "表格中没有数据可导出，请先加载违例数据")
            return

        # 构建默认保存路径
        corner_name = self.get_display_corner()
        default_dir = os.path.join(os.getcwd(), "VIOLATION_CHECK", corner_name)

        # 确保目录存在
        os.makedirs(default_dir, exist_ok=True)

        default_name = f"{self.current_case_name}_{corner_name}_violations_checklist.xlsx"
        default_path = os.path.join(default_dir, default_name)

        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出Excel文件", default_path, "Excel文件 (*.xlsx)"
        )

        if file_path:
            try:
                self._export_data_to_excel(file_path)
                QMessageBox.information(self, "成功", f"数据已导出到: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")

    def export_to_csv(self):
        """导出为CSV文件"""
        if not self.current_case_name:
            QMessageBox.warning(self, "警告", "没有数据可导出")
            return

        # 检查表格中是否有数据
        table_row_count = self.violation_table.rowCount()
        print(f"导出CSV - 表格中有 {table_row_count} 行数据")

        if table_row_count == 0:
            QMessageBox.warning(self, "警告", "表格中没有数据可导出，请先加载违例数据")
            return

        # 构建默认保存路径
        corner_name = self.get_display_corner()
        default_dir = os.path.join(os.getcwd(), "VIOLATION_CHECK", corner_name)

        # 确保目录存在
        os.makedirs(default_dir, exist_ok=True)

        default_name = f"{self.current_case_name}_{corner_name}_violations_checklist.csv"
        default_path = os.path.join(default_dir, default_name)

        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出CSV文件", default_path, "CSV文件 (*.csv)"
        )

        if file_path:
            try:
                self._export_data_to_csv(file_path)
                QMessageBox.information(self, "成功", f"数据已导出到: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")

    def apply_historical_confirmations(self):
        """手动应用历史确认记录"""
        if not self.current_case_name:
            QMessageBox.warning(self, "警告", "请先选择日志文件")
            return

        try:
            corner = self.current_corner if self.current_corner else "default"
            applied_count = self.data_model.apply_historical_confirmations(
                self.current_case_name, corner
            )

            if applied_count > 0:
                QMessageBox.information(
                    self, "成功",
                    f"已自动应用 {applied_count} 条历史确认记录"
                )
                # 使用QTimer延迟更新UI，避免死锁
                QTimer.singleShot(0, self.safe_update_ui)
            else:
                QMessageBox.information(
                    self, "提示",
                    "没有找到可应用的历史确认记录"
                )
        except Exception as e:
            QMessageBox.critical(self, "错误", f"应用历史确认失败: {str(e)}")

    def clear_history(self):
        """清除历史数据"""
        if not self.current_case_name:
            QMessageBox.warning(self, "警告", "没有数据可清除")
            return

        reply = QMessageBox.question(
            self, "确认",
            f"确定要清除用例 '{self.current_case_name}' 的所有历史数据吗？\n此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                corner = self.current_corner if self.current_corner else "default"
                self.data_model.clear_case_data(self.current_case_name, corner)
                self.update_violation_table()
                self.update_progress_display()
                QMessageBox.information(self, "成功", "历史数据已清除")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"清除数据失败: {str(e)}")

    def show_history_management(self):
        """显示历史管理对话框"""
        try:
            dialog = HistoryManagementDialog(self, self.data_model)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开历史管理失败: {str(e)}")

    def _export_data_to_excel(self, file_path: str):
        """导出数据到Excel文件"""
        try:
            import openpyxl
            from openpyxl.styles import Font, Alignment, PatternFill
        except ImportError:
            raise ImportError("需要安装openpyxl库: pip install openpyxl")

        # 获取数据 - 优先从数据库获取，如果没有则提示用户
        stored_corner = self.get_stored_corner()
        violations = self.data_model.get_violations_by_case(self.current_case_name, stored_corner)

        # 调试信息
        print(f"导出Excel - 用例: {self.current_case_name}, Corner: {stored_corner}")
        print(f"导出Excel - 从数据库获取到 {len(violations)} 条违例记录")

        # 如果数据库中没有数据，尝试从当前内存中的数据获取
        if not violations and hasattr(self, 'current_violations') and self.current_violations:
            print("导出Excel - 数据库中无数据，使用内存中的数据")
            violations = self.current_violations
            print(f"导出Excel - 从内存获取到 {len(violations)} 条违例记录")
            # 调试：显示第一条记录的字段
            if violations:
                first_record = violations[0]
                print(f"导出Excel - 数据格式示例: {list(first_record.keys())[:5]}...")

        if not violations:
            raise ValueError(f"没有找到用例 '{self.current_case_name}' (corner: {stored_corner}) 的违例数据。请先加载违例日志文件。")

        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "时序违例确认清单"

        # 设置表头
        headers = ["序号", "层级路径", "时间(ns)", "检查信息", "状态", "确认人", "确认结果", "确认理由", "确认时间"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        # 填充数据
        for row, violation in enumerate(violations, 2):
            # 兼容两种数据格式：数据库格式(小写)和解析器格式(大写)
            num = violation.get('num', violation.get('NUM', ''))
            hier = violation.get('hier', violation.get('Hier', ''))
            time_fs = violation.get('time_fs', violation.get('time_fs', 0))
            check_info = violation.get('check_info', violation.get('Check', ''))
            status = violation.get('status', '')
            confirmer = violation.get('confirmer', '')
            result = violation.get('result', '')
            reason = violation.get('reason', '')
            confirmed_at = violation.get('confirmed_at', '')

            print(f"导出Excel - 处理第 {row-1} 条记录: {hier[:50]}...")

            ws.cell(row=row, column=1, value=num)
            ws.cell(row=row, column=2, value=hier)
            ws.cell(row=row, column=3, value=time_fs / 1000000 if time_fs else 0)
            ws.cell(row=row, column=4, value=check_info)
            ws.cell(row=row, column=5, value=self.get_status_display(status))
            ws.cell(row=row, column=6, value=confirmer)
            ws.cell(row=row, column=7, value=self.get_result_display(result))
            ws.cell(row=row, column=8, value=reason)
            ws.cell(row=row, column=9, value=confirmed_at)

        print(f"导出Excel - 完成数据填充，共处理 {len(violations)} 条记录")

        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 保存文件
        wb.save(file_path)

    def _export_data_to_csv(self, file_path: str):
        """导出数据到CSV文件"""
        import csv

        # 获取数据 - 优先从数据库获取，如果没有则提示用户
        stored_corner = self.get_stored_corner()
        violations = self.data_model.get_violations_by_case(self.current_case_name, stored_corner)

        # 调试信息
        print(f"导出CSV - 用例: {self.current_case_name}, Corner: {stored_corner}")
        print(f"导出CSV - 从数据库获取到 {len(violations)} 条违例记录")

        # 如果数据库中没有数据，尝试从当前内存中的数据获取
        if not violations and hasattr(self, 'current_violations') and self.current_violations:
            print("导出CSV - 数据库中无数据，使用内存中的数据")
            violations = self.current_violations
            print(f"导出CSV - 从内存获取到 {len(violations)} 条违例记录")
            # 调试：显示第一条记录的字段
            if violations:
                first_record = violations[0]
                print(f"导出CSV - 数据格式示例: {list(first_record.keys())[:5]}...")

        if not violations:
            raise ValueError(f"没有找到用例 '{self.current_case_name}' (corner: {stored_corner}) 的违例数据。请先加载违例日志文件。")

        # 写入CSV文件
        with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)

            # 写入表头
            headers = ["序号", "层级路径", "时间(ns)", "检查信息", "状态", "确认人", "确认结果", "确认理由", "确认时间"]
            writer.writerow(headers)

            # 写入数据
            for i, violation in enumerate(violations):
                # 兼容两种数据格式：数据库格式(小写)和解析器格式(大写)
                num = violation.get('num', violation.get('NUM', ''))
                hier = violation.get('hier', violation.get('Hier', ''))
                time_fs = violation.get('time_fs', violation.get('time_fs', 0))
                check_info = violation.get('check_info', violation.get('Check', ''))
                status = violation.get('status', '')
                confirmer = violation.get('confirmer', '')
                result = violation.get('result', '')
                reason = violation.get('reason', '')
                confirmed_at = violation.get('confirmed_at', '')

                print(f"导出CSV - 处理第 {i+1} 条记录: {hier[:50]}...")

                row = [
                    num,
                    hier,
                    time_fs / 1000000 if time_fs else 0,
                    check_info,
                    self.get_status_display(status),
                    confirmer,
                    self.get_result_display(result),
                    reason,
                    confirmed_at
                ]
                writer.writerow(row)

            print(f"导出CSV - 完成数据写入，共处理 {len(violations)} 条记录")


class ConfirmationDialog(QDialog):
    """确认对话框"""

    def __init__(self, parent=None, violation=None, suggestions=None, edit_mode=False):
        super().__init__(parent)
        self.violation = violation
        self.suggestions = suggestions
        self.edit_mode = edit_mode

        self.setWindowTitle("编辑确认信息" if edit_mode else "确认时序违例")
        self.setModal(True)
        self.resize(600, 400)

        # 设置窗口标志，确保对话框正常显示
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        self.init_ui()
        if not edit_mode:  # 只在非编辑模式下应用建议
            self.apply_suggestions()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)

        # 违例信息显示
        if self.violation:
            info_group = QGroupBox("违例信息")
            info_layout = QFormLayout(info_group)

            info_layout.addRow("序号:", QLabel(str(self.violation.get('num', ''))))
            info_layout.addRow("层级路径:", QLabel(self.violation.get('hier', '')))

            time_ns = self.violation.get('time_fs', 0) / 1000000
            info_layout.addRow("时间:", QLabel(f"{time_ns:.3f} ns"))

            check_label = QLabel(self.violation.get('check_info', ''))
            check_label.setWordWrap(True)
            info_layout.addRow("检查信息:", check_label)

            layout.addWidget(info_group)

        # 确认信息输入
        confirm_group = QGroupBox("确认信息")
        confirm_layout = QFormLayout(confirm_group)

        # 确认人
        self.confirmer_edit = QLineEdit()
        self.confirmer_edit.setPlaceholderText("请输入确认人姓名")
        confirm_layout.addRow("确认人*:", self.confirmer_edit)

        # 确认结果
        result_layout = QHBoxLayout()
        self.result_group = QButtonGroup()

        self.pass_radio = QRadioButton("通过")
        self.issue_radio = QRadioButton("有问题")
        self.pass_radio.setChecked(True)  # 默认选择通过

        self.result_group.addButton(self.pass_radio, 0)
        self.result_group.addButton(self.issue_radio, 1)

        result_layout.addWidget(self.pass_radio)
        result_layout.addWidget(self.issue_radio)
        result_layout.addStretch()

        confirm_layout.addRow("确认结果*:", result_layout)

        # 确认理由
        self.reason_edit = QTextEdit()
        self.reason_edit.setPlaceholderText("请输入确认理由或解决方案")
        self.reason_edit.setMaximumHeight(100)
        confirm_layout.addRow("确认理由*:", self.reason_edit)

        layout.addWidget(confirm_group)

        # 历史建议
        if self.suggestions and not self.edit_mode:
            suggestion_group = QGroupBox("历史建议")
            suggestion_layout = QVBoxLayout(suggestion_group)

            suggestion_text = f"确认人: {self.suggestions.get('confirmer', '')}\n"
            suggestion_text += f"确认结果: {self.suggestions.get('result', '')}\n"
            suggestion_text += f"确认理由: {self.suggestions.get('reason', '')}\n"
            suggestion_text += f"使用次数: {self.suggestions.get('match_count', 0)}"

            suggestion_label = QLabel(suggestion_text)
            suggestion_label.setWordWrap(True)
            suggestion_layout.addWidget(suggestion_label)

            apply_btn = QPushButton("应用建议")
            apply_btn.clicked.connect(self.apply_suggestions)
            suggestion_layout.addWidget(apply_btn)

            layout.addWidget(suggestion_group)

        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # 如果是编辑模式，填充现有数据
        if self.edit_mode and self.violation:
            self.confirmer_edit.setText(self.violation.get('confirmer', ''))

            result = self.violation.get('result', '')
            if result == 'pass':
                self.pass_radio.setChecked(True)
            elif result == 'issue':
                self.issue_radio.setChecked(True)

            self.reason_edit.setPlainText(self.violation.get('reason', ''))

    def apply_suggestions(self):
        """应用历史建议"""
        if not self.suggestions:
            return

        self.confirmer_edit.setText(self.suggestions.get('confirmer', ''))

        result = self.suggestions.get('result', '')
        if result == 'pass':
            self.pass_radio.setChecked(True)
        elif result == 'issue':
            self.issue_radio.setChecked(True)

        self.reason_edit.setPlainText(self.suggestions.get('reason', ''))

    def accept(self):
        """确认按钮点击（移除验证弹窗）"""
        # 静默验证输入，不显示弹窗
        if not self.confirmer_edit.text().strip():
            print("警告: 请输入确认人姓名")
            # 设置焦点到确认人输入框
            self.confirmer_edit.setFocus()
            return

        if not self.reason_edit.toPlainText().strip():
            print("警告: 请输入确认理由")
            # 设置焦点到理由输入框
            self.reason_edit.setFocus()
            return

        super().accept()

    def get_result(self):
        """获取确认结果"""
        result = 'pass' if self.pass_radio.isChecked() else 'issue'

        return {
            'confirmer': self.confirmer_edit.text().strip(),
            'result': result,
            'reason': self.reason_edit.toPlainText().strip()
        }


class BatchConfirmationDialog(QDialog):
    """批量确认对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("批量确认")
        self.setModal(True)
        self.resize(400, 250)

        # 设置窗口标志，确保对话框正常显示
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)

        # 说明
        info_label = QLabel("请填写批量确认信息，将应用到所有选中的违例记录：")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 确认信息输入
        form_layout = QFormLayout()

        # 确认人
        self.confirmer_edit = QLineEdit()
        self.confirmer_edit.setPlaceholderText("请输入确认人姓名")
        form_layout.addRow("确认人*:", self.confirmer_edit)

        # 确认结果
        result_layout = QHBoxLayout()
        self.result_group = QButtonGroup()

        self.pass_radio = QRadioButton("通过")
        self.issue_radio = QRadioButton("有问题")
        self.pass_radio.setChecked(True)  # 默认选择通过

        self.result_group.addButton(self.pass_radio, 0)
        self.result_group.addButton(self.issue_radio, 1)

        result_layout.addWidget(self.pass_radio)
        result_layout.addWidget(self.issue_radio)
        result_layout.addStretch()

        form_layout.addRow("确认结果*:", result_layout)

        # 确认理由
        self.reason_edit = QTextEdit()
        self.reason_edit.setPlaceholderText("请输入确认理由或解决方案")
        self.reason_edit.setMaximumHeight(80)
        form_layout.addRow("确认理由*:", self.reason_edit)

        layout.addLayout(form_layout)

        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def accept(self):
        """确认按钮点击（移除验证弹窗）"""
        # 静默验证输入，不显示弹窗
        if not self.confirmer_edit.text().strip():
            print("警告: 请输入确认人姓名")
            # 设置焦点到确认人输入框
            self.confirmer_edit.setFocus()
            return

        if not self.reason_edit.toPlainText().strip():
            print("警告: 请输入确认理由")
            # 设置焦点到理由输入框
            self.reason_edit.setFocus()
            return

        super().accept()

    def get_result(self):
        """获取确认结果"""
        result = 'pass' if self.pass_radio.isChecked() else 'issue'

        return {
            'confirmer': self.confirmer_edit.text().strip(),
            'result': result,
            'reason': self.reason_edit.toPlainText().strip()
        }

class HistoryManagementDialog(QDialog):
    """历史管理对话框"""

    def __init__(self, parent=None, data_model=None):
        super().__init__(parent)
        self.data_model = data_model

        self.setWindowTitle("历史确认模式管理")
        self.setModal(True)
        self.resize(1000, 600)

        # 设置窗口标志
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        self.init_ui()
        self.load_patterns()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)

        # 说明标签
        info_label = QLabel("历史确认模式管理 - 显示所有已保存的确认模式，可以查看和删除")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 工具栏
        toolbar_layout = QHBoxLayout()

        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.load_patterns)
        toolbar_layout.addWidget(self.refresh_btn)

        self.clear_all_btn = QPushButton("清除全部")
        self.clear_all_btn.clicked.connect(self.clear_all_patterns)
        toolbar_layout.addWidget(self.clear_all_btn)

        toolbar_layout.addStretch()

        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)
        toolbar_layout.addWidget(self.close_btn)

        layout.addLayout(toolbar_layout)

        # 历史模式表格
        self.patterns_table = QTableWidget()
        self.patterns_table.setColumnCount(7)

        headers = ["层级路径", "检查信息", "确认人", "确认结果", "确认理由", "使用次数", "最后使用时间"]
        self.patterns_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.patterns_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.patterns_table.setAlternatingRowColors(True)
        self.patterns_table.setSortingEnabled(True)

        # 设置列宽
        header = self.patterns_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # 层级路径
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 检查信息
        header.setSectionResizeMode(2, QHeaderView.Fixed)    # 确认人
        header.setSectionResizeMode(3, QHeaderView.Fixed)    # 确认结果
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # 确认理由
        header.setSectionResizeMode(5, QHeaderView.Fixed)    # 使用次数
        header.setSectionResizeMode(6, QHeaderView.Fixed)    # 最后使用时间

        # 设置固定列宽
        self.patterns_table.setColumnWidth(2, 100)  # 确认人
        self.patterns_table.setColumnWidth(3, 80)   # 确认结果
        self.patterns_table.setColumnWidth(5, 80)   # 使用次数
        self.patterns_table.setColumnWidth(6, 150)  # 最后使用时间

        layout.addWidget(self.patterns_table)

        # 统计信息
        self.stats_label = QLabel("总计: 0 个历史模式")
        layout.addWidget(self.stats_label)

    def load_patterns(self):
        """加载历史模式"""
        try:
            patterns = self.data_model.get_all_patterns()

            # 设置表格行数
            self.patterns_table.setRowCount(len(patterns))

            # 填充数据
            for row, pattern in enumerate(patterns):
                # 层级路径
                hier_item = QTableWidgetItem(pattern.get('hier_pattern', ''))
                self.patterns_table.setItem(row, 0, hier_item)

                # 检查信息
                check_item = QTableWidgetItem(pattern.get('check_pattern', ''))
                self.patterns_table.setItem(row, 1, check_item)

                # 确认人
                confirmer_item = QTableWidgetItem(pattern.get('default_confirmer', ''))
                confirmer_item.setTextAlignment(Qt.AlignCenter)
                self.patterns_table.setItem(row, 2, confirmer_item)

                # 确认结果
                result = pattern.get('default_result', '')
                result_display = "通过" if result == "pass" else "有问题" if result == "issue" else result
                result_item = QTableWidgetItem(result_display)
                result_item.setTextAlignment(Qt.AlignCenter)
                self.patterns_table.setItem(row, 3, result_item)

                # 确认理由
                reason_item = QTableWidgetItem(pattern.get('default_reason', ''))
                self.patterns_table.setItem(row, 4, reason_item)

                # 使用次数
                count_item = QTableWidgetItem(str(pattern.get('match_count', 0)))
                count_item.setTextAlignment(Qt.AlignCenter)
                self.patterns_table.setItem(row, 5, count_item)

                # 最后使用时间
                last_used = pattern.get('last_used', '')
                time_item = QTableWidgetItem(last_used)
                time_item.setTextAlignment(Qt.AlignCenter)
                self.patterns_table.setItem(row, 6, time_item)

            # 更新统计信息
            self.stats_label.setText(f"总计: {len(patterns)} 个历史模式")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载历史模式失败: {str(e)}")

    def clear_all_patterns(self):
        """清除所有历史模式"""
        reply = QMessageBox.question(
            self, "确认",
            "确定要清除所有历史确认模式吗？\n此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                success = self.data_model.clear_all_patterns()
                if success:
                    QMessageBox.information(self, "成功", "已清除所有历史模式")
                    self.load_patterns()  # 重新加载
                else:
                    QMessageBox.warning(self, "警告", "清除历史模式失败")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"清除历史模式失败: {str(e)}")


class AsyncDataProcessor(QThread):
    """异步数据处理器 - 用于大数据量的后台处理"""

    # 信号定义
    stage_completed = pyqtSignal(str, int)  # 阶段名称, 进度
    processing_finished = pyqtSignal(dict)  # 处理结果
    processing_failed = pyqtSignal(str)     # 错误信息

    def __init__(self, violations, case_name, corner, file_path, data_model):
        super().__init__()
        self.violations = violations
        self.case_name = case_name
        self.corner = corner
        self.file_path = file_path
        self.data_model = data_model
        self._is_cancelled = False

    def cancel(self):
        """取消处理"""
        self._is_cancelled = True

    def run(self):
        """异步处理主函数"""
        try:
            result_data = {}

            # 阶段1：数据库清理 (98%-98.5%)
            self.stage_completed.emit("清理旧数据", 98)
            if self._is_cancelled:
                return

            if self.case_name:
                self.data_model.clear_case_data(self.case_name, self.corner)

            # 阶段2：批量插入违例记录 (98.5%-99%)
            self.stage_completed.emit("添加违例记录", 98)
            if self._is_cancelled:
                return

            success_count = 0
            if self.violations and self.case_name:
                success_count = self.data_model.add_violations(
                    self.violations, self.case_name, self.corner, self.file_path
                )
                print(f"异步处理：成功添加 {success_count} 条违例记录到数据库")

            result_data['success_count'] = success_count

            # 阶段3：应用历史确认 (99%-99.5%)
            self.stage_completed.emit("应用历史确认", 99)
            if self._is_cancelled:
                return

            applied_count = 0
            if success_count > 0:
                applied_count = self.data_model.apply_historical_confirmations(
                    self.case_name, self.corner
                )
                print(f"异步处理：自动应用历史确认 {applied_count} 条")

            result_data['applied_count'] = applied_count

            # 阶段4：清理缓存 (99.5%-100%)
            self.stage_completed.emit("清理缓存", 99)
            if self._is_cancelled:
                return

            if hasattr(self.data_model, 'clear_cache'):
                self.data_model.clear_cache()
                print("异步处理：已清除查询缓存")

            # 完成
            self.stage_completed.emit("完成", 100)
            self.processing_finished.emit(result_data)

        except Exception as e:
            error_msg = f"异步数据处理失败: {str(e)}"
            print(error_msg)
            self.processing_failed.emit(error_msg)
