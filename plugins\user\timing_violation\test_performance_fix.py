#!/usr/bin/env python3
"""
时序违例插件性能修复测试脚本

测试大数据量场景下的性能优化效果，验证修复是否解决了以下问题：
1. QLayout重复添加错误
2. 98%进度后的GUI卡死
3. 大数据量处理性能瓶颈
4. 虚拟滚动和异步处理效果
"""

import sys
import os
import time
import random
from typing import List, Dict

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QProgressBar
from PyQt5.QtCore import QTimer, pyqtSignal
from PyQt5.QtGui import QFont

# 导入插件模块
from plugins.user.timing_violation.main_window import TimingViolationWindow, AsyncDataProcessor
from plugins.user.timing_violation.models import ViolationDataModel


class PerformanceTestWindow(QMainWindow):
    """性能测试主窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("时序违例插件性能修复测试")
        self.setGeometry(100, 100, 800, 600)

        # 创建测试数据
        self.test_data_sizes = [100, 500, 1000, 2000, 5000, 10000]
        self.current_test_index = 0
        self.test_results = {}

        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # 标题
        title_label = QLabel("时序违例插件性能修复测试")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        layout.addWidget(title_label)

        # 测试说明
        info_label = QLabel("""
测试内容：
1. QLayout重复添加错误修复验证
2. 大数据量处理性能测试
3. 虚拟滚动效果验证
4. 异步处理效果验证
5. 内存使用优化验证

测试数据量：100, 500, 1000, 2000, 5000, 10000 条记录
        """)
        layout.addWidget(info_label)

        # 进度条
        self.progress_bar = QProgressBar()
        layout.addWidget(self.progress_bar)

        # 状态标签
        self.status_label = QLabel("准备开始测试...")
        layout.addWidget(self.status_label)

        # 结果显示
        self.result_label = QLabel("")
        layout.addWidget(self.result_label)

        # 测试按钮
        self.start_test_btn = QPushButton("开始性能测试")
        self.start_test_btn.clicked.connect(self.start_performance_test)
        layout.addWidget(self.start_test_btn)

        # 单独测试按钮
        self.test_layout_btn = QPushButton("测试布局修复")
        self.test_layout_btn.clicked.connect(self.test_layout_fix)
        layout.addWidget(self.test_layout_btn)

        self.test_virtual_scroll_btn = QPushButton("测试虚拟滚动")
        self.test_virtual_scroll_btn.clicked.connect(self.test_virtual_scroll)
        layout.addWidget(self.test_virtual_scroll_btn)

        self.test_async_btn = QPushButton("测试异步处理")
        self.test_async_btn.clicked.connect(self.test_async_processing)
        layout.addWidget(self.test_async_btn)

    def generate_test_data(self, count: int) -> List[Dict]:
        """生成测试数据"""
        violations = []
        for i in range(count):
            violation = {
                'NUM': i + 1,
                'Hier': f'tb.cpu.core{i % 10}.unit{i % 5}.reg{i % 3}',
                'time_fs': random.randint(1000000, 10000000),  # 1-10ms in fs
                'Time': f"{random.randint(1, 10)}.{random.randint(100, 999)}ns",
                'Check': f'setup_check_{i % 20}' if i % 2 == 0 else f'hold_check_{i % 15}',
                'id': i + 1,
                'status': 'pending',
                'confirmer': '',
                'result': '',
                'reason': ''
            }
            violations.append(violation)
        return violations

    def start_performance_test(self):
        """开始性能测试"""
        self.start_test_btn.setEnabled(False)
        self.current_test_index = 0
        self.test_results = {}
        self.progress_bar.setRange(0, len(self.test_data_sizes))
        self.progress_bar.setValue(0)

        self.run_next_test()

    def run_next_test(self):
        """运行下一个测试"""
        if self.current_test_index >= len(self.test_data_sizes):
            self.show_test_results()
            return

        data_size = self.test_data_sizes[self.current_test_index]
        self.status_label.setText(f"测试 {data_size} 条记录...")

        # 生成测试数据
        test_data = self.generate_test_data(data_size)

        # 记录开始时间
        start_time = time.time()

        try:
            # 创建时序违例窗口
            violation_window = TimingViolationWindow()
            violation_window.show()

            # 模拟数据加载
            violation_window.current_violations = test_data
            violation_window.current_case_name = f"test_case_{data_size}"
            violation_window.current_corner = "test_corner"

            # 测试表格更新
            violation_window.update_violation_table()

            # 记录结束时间
            end_time = time.time()
            load_time = end_time - start_time

            # 记录测试结果
            self.test_results[data_size] = {
                'load_time': load_time,
                'success': True,
                'error': None
            }

            print(f"测试 {data_size} 条记录完成，耗时: {load_time:.3f}秒")

            # 关闭窗口
            violation_window.close()

        except Exception as e:
            end_time = time.time()
            load_time = end_time - start_time

            self.test_results[data_size] = {
                'load_time': load_time,
                'success': False,
                'error': str(e)
            }

            print(f"测试 {data_size} 条记录失败: {e}")

        # 更新进度
        self.current_test_index += 1
        self.progress_bar.setValue(self.current_test_index)

        # 延迟运行下一个测试，避免内存问题
        QTimer.singleShot(1000, self.run_next_test)

    def show_test_results(self):
        """显示测试结果"""
        self.start_test_btn.setEnabled(True)
        self.status_label.setText("测试完成！")

        result_text = "性能测试结果：\n\n"
        for data_size, result in self.test_results.items():
            if result['success']:
                result_text += f"{data_size:>6} 条记录: {result['load_time']:>6.3f}秒 ✓\n"
            else:
                result_text += f"{data_size:>6} 条记录: 失败 - {result['error'][:50]}...\n"

        # 性能分析
        successful_tests = {k: v for k, v in self.test_results.items() if v['success']}
        if successful_tests:
            avg_time_per_record = sum(v['load_time'] for v in successful_tests.values()) / sum(successful_tests.keys())
            result_text += f"\n平均每条记录处理时间: {avg_time_per_record*1000:.3f}ms\n"

            # 检查性能阈值
            large_data_tests = {k: v for k, v in successful_tests.items() if k >= 1000}
            if large_data_tests:
                max_time = max(v['load_time'] for v in large_data_tests.values())
                if max_time < 5.0:
                    result_text += "✓ 大数据量性能测试通过（<5秒）\n"
                else:
                    result_text += f"⚠ 大数据量性能需要优化（最大耗时: {max_time:.3f}秒）\n"

        self.result_label.setText(result_text)

    def test_layout_fix(self):
        """测试布局修复"""
        self.status_label.setText("测试布局修复...")

        try:
            # 创建时序违例窗口
            violation_window = TimingViolationWindow()
            violation_window.show()

            # 生成测试数据
            test_data = self.generate_test_data(100)

            # 多次更新数据，测试布局重复添加问题
            for i in range(5):
                violation_window.current_violations = test_data
                violation_window.update_violation_table()
                QApplication.processEvents()
                time.sleep(0.1)

            self.status_label.setText("布局修复测试完成 ✓")
            violation_window.close()

        except Exception as e:
            self.status_label.setText(f"布局修复测试失败: {e}")

    def test_virtual_scroll(self):
        """测试虚拟滚动"""
        self.status_label.setText("测试虚拟滚动...")

        try:
            # 创建时序违例窗口
            violation_window = TimingViolationWindow()
            violation_window.show()

            # 生成大量测试数据
            test_data = self.generate_test_data(2000)
            violation_window.current_violations = test_data
            violation_window.current_case_name = "virtual_scroll_test"

            # 更新表格，应该触发虚拟滚动
            violation_window.update_violation_table()

            # 检查是否启用了虚拟滚动
            if hasattr(violation_window, 'high_performance_table'):
                table = violation_window.high_performance_table
                if hasattr(table, 'enable_virtual_scroll') and table.enable_virtual_scroll:
                    self.status_label.setText("虚拟滚动测试完成 ✓")
                else:
                    self.status_label.setText("虚拟滚动未启用")
            else:
                self.status_label.setText("高性能表格未创建")

            violation_window.close()

        except Exception as e:
            self.status_label.setText(f"虚拟滚动测试失败: {e}")

    def test_async_processing(self):
        """测试异步处理"""
        self.status_label.setText("测试异步处理...")

        try:
            # 创建数据模型
            data_model = ViolationDataModel()

            # 生成大量测试数据
            test_data = self.generate_test_data(1500)

            # 创建异步处理器
            processor = AsyncDataProcessor(
                test_data, "async_test_case", "test_corner",
                "/test/path", data_model
            )

            # 连接信号
            def on_stage_completed(stage_name, progress):
                self.status_label.setText(f"异步处理: {stage_name} ({progress}%)")

            def on_processing_finished(result_data):
                success_count = result_data.get('success_count', 0)
                applied_count = result_data.get('applied_count', 0)
                self.status_label.setText(f"异步处理完成 ✓ (添加: {success_count}, 应用: {applied_count})")

            def on_processing_failed(error_msg):
                self.status_label.setText(f"异步处理失败: {error_msg}")

            processor.stage_completed.connect(on_stage_completed)
            processor.processing_finished.connect(on_processing_finished)
            processor.processing_failed.connect(on_processing_failed)

            # 启动异步处理
            processor.start()

        except Exception as e:
            self.status_label.setText(f"异步处理测试失败: {e}")


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 创建测试窗口
    test_window = PerformanceTestWindow()
    test_window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()